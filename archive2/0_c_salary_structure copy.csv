"ID","Company","Is Active","Currency","Letter Head","Is Default","Amended From","Leave Encashment Amount Per Day","Max Benefits (Amount)","Salary Slip Based on Timesheet","Payroll Frequency","Salary Component","Hour Rate","Total Earning","Total Deduction","Net Pay","Mode of Payment","Payment Account","ID (Earnings)","Abbr (Earnings)","Additional Amount (Earnings)","Additional Salary  (Earnings)","Amount (Earnings)","Amount based on formula (Earnings)","Component (Earnings)","Condition (Earnings)","Deduct Full Tax on Selected Payroll Date (Earnings)","Default Amount (Earnings)","Depends on Payment Days (Earnings)","Do not include in total (Earnings)","Exempted from Income Tax (Earnings)","Formula (Earnings)","Is Flexible Benefit (Earnings)","Is Recurring Additional Salary (Earnings)","Is Tax Applicable (Earnings)","Statistical Component (Earnings)","Tax on additional salary (Earnings)","Tax on flexible benefit (Earnings)","Variable Based On Taxable Salary (Earnings)","Year To Date (Earnings)","ID (Deductions)","Abbr (Deductions)","Additional Amount (Deductions)","Additional Salary  (Deductions)","Amount (Deductions)","Amount based on formula (Deductions)","Component (Deductions)","Condition (Deductions)","Deduct Full Tax on Selected Payroll Date (Deductions)","Default Amount (Deductions)","Depends on Payment Days (Deductions)","Do not include in total (Deductions)","Exempted from Income Tax (Deductions)","Formula (Deductions)","Is Flexible Benefit (Deductions)","Is Recurring Additional Salary (Deductions)","Is Tax Applicable (Deductions)","Statistical Component (Deductions)","Tax on additional salary (Deductions)","Tax on flexible benefit (Deductions)","Variable Based On Taxable Salary (Deductions)","Year To Date (Deductions)"
"15-5U","NHYPPADEC","Yes","NGN","","No","",0.0,0.0,0,"Monthly","",0.0,0.0,0.0,0.0,"Wire Transfer","Bank Account - NHYP","o9nt3um308","BS",0.0,"",0.0,1,"Basic","",0,0.0,0,0,0,"base*0.108/12",0,0,1,0,0.0,0.0,0,0.0,"o9nki79eb3","NPF",0.0,"",0.0,1,"NPF","",0,0.0,0,0,0,"((base * 0.108) * 0.08) / 12",0,0,0,0,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n44j8iup","MA",0.0,"",0.0,1,"Meal Allowance","",0,0.0,0,0,0,"base * 0.011/12",0,0,1,0,0.0,0.0,0,0.0,"o9n0agu4t7","NHF",0.0,"",0.0,1,"NHF","",0,0.0,0,0,0,"((base * 0.108) * 0.025) / 12",0,0,0,0,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n8egse5k","EA",0.0,"",0.0,1,"Entertainment Allowance","",0,0.0,0,0,0,"base * 0.02/12",0,0,1,0,0.0,0.0,0,0.0,"o9nmqbppvo","NHIS",0.0,"",0.0,1,"NHIS","",0,0.0,0,0,0,"((base * 0.108) * 0.05) / 12",0,0,0,0,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n3tk91fn","DA",0.0,"",0.0,1,"Domestic Allowance","",0,0.0,0,0,0,"base * 0.05/12",0,0,1,0,0.0,0.0,0,0.0,"o9nhafh08s","TAE",0.0,"",0.0,1,"Total Annual Earnings","",0,0.0,0,0,0,"(
  (base * 0.108) +
  (base * 0.108) +
  (base * 0.108) +
  ((base * 0.10) if grade not in [""HYPPSL 17"", ""HYPPSL 16"", ""HYPPSL 15""] else 0) +
  (base * 0.07) +
  (base * 0.05) +
  (base * 0.03) +
  (base * 0.03) +
  (base * 0.02) +
  (base * 0.011) +
  (base * 0.007) +
  (base * 0.005) +
  (base * 0.004)
)",0,0,0,1,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9nf4jet0m","UA",0.0,"",0.0,1,"Utility Allowance","",0,0.0,0,0,0,"base * 0.03/12",0,0,1,0,0.0,0.0,0,0.0,"o9n8i0b48e","GI_FOR_CRA",0.0,"",0.0,1,"Gross Income for CRA","",0,0.0,1,0,0,"TAE - ( (NPF * 12) + (NHF * 12) + (NHIS * 12) )",0,0,0,1,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n1d7bd10","MDA",0.0,"",0.0,1,"Medical Allowance","",0,0.0,0,0,0,"base * 0.03/12",0,0,1,0,0.0,0.0,0,0.0,"o9ncqs8dts","ACRA",0.0,"",0.0,1,"Annual CRA","",0,0.0,0,0,0,"(GI_FOR_CRA * 0.2) + 200000",0,0,0,1,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9nvqhanud","DSTV",0.0,"",0.0,1,"DSTV Allowance","",0,0.0,0,0,0,"base * 0.004/12",0,0,1,0,0.0,0.0,0,0.0,"o9nu71i65n","ACI",0.0,"",0.0,1,"Annual Chargeable Income","",0,0.0,0,0,0,"TAE - ACRA",0,0,0,1,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n7tamk6t","INT",0.0,"",0.0,1,"Internet Allowance","",0,0.0,0,0,0,"base * 0.005/12",0,0,1,0,0.0,0.0,0,0.0,"o9nhgsmvas","PAYE",0.0,"",0.0,1,"PAYE","",0,0.0,0,0,0,"( ( (ACI > 0 and ACI <= 300000) * (ACI * 0.07) ) + ( (ACI > 300000 and ACI <= 600000) * ((ACI - 300000) * 0.11 + 21000) ) + ( (ACI > 600000 and ACI <= 1100000) * ((ACI - 600000) * 0.15 + 54000) ) + ( (ACI > 1100000 and ACI <= 1600000) * ((ACI - 1100000) * 0.19 + 129000) ) + ( (ACI > 1600000 and ACI <= 3200000) * ((ACI - 1600000) * 0.21 + 224000) ) + ( (ACI > 3200000) * ((ACI - 3200000) * 0.24 + 560000) ) ) / 12",0,0,0,0,0.0,0.0,0,0.0
"","","","","","","","","","","","","","","","","","","o9n3kdo5ou","TEL",0.0,"",0.0,1,"Telephone Allowance","",0,0.0,0,0,0,"base * 0.007/12",0,0,1,0,0.0,0.0,0,0.0,"","","","","","","","","","","","","","","","","","","","","",""
"","","","","","","","","","","","","","","","","","","o9nk60hi9p","EDU",0.0,"",0.0,1,"Education Allowance","",0,0.0,0,0,0,"base * 0.07/12",0,0,1,0,0.0,0.0,0,0.0,"","","","","","","","","","","","","","","","","","","","","",""
"","","","","","","","","","","","","","","","","","","o9n1be5hts","HAZ",0.0,"",0.0,1,"Hazard Allowance","",0,0.0,0,0,0,"base * 0.108/12",0,0,1,0,0.0,0.0,0,0.0,"","","","","","","","","","","","","","","","","","","","","",""
"","","","","","","","","","","","","","","","","","","o9nk7ar8vt","WA",0.0,"",0.0,1,"Wardrobe Allowance","",0,0.0,0,0,0,"base * 0.108/12",0,0,1,0,0.0,0.0,0,0.0,"","","","","","","","","","","","","","","","","","","","","",""
"","","","","","","","","","","","","","","","","","","o9nkm0t8i9","RPA",0.0,"",0.0,1,"Rural Posting Allowance","grade not in [""HYPPSL 17"", ""HYPPSL 16"", ""HYPPSL 15""]",0,0.0,0,0,0,"base * 0.10/12",0,0,1,0,0.0,0.0,0,0.0,"","","","","","","","","","","","","","","","","","","","","",""
