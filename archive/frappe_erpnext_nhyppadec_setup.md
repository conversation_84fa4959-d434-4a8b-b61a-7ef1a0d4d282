# Frappe/ERPNext Setup Guide for office.nhyppadec.gov.ng

This guide provides a concise set of steps to set up a Frappe/ERPNext instance with a site for `office.nhyppadec.gov.ng`, based on the provided command history.

## 1. Nginx Configuration

1.  **Link Nginx Configuration:**

    ```bash
    sudo ln -s /home/<USER>/frappe-bench/config/nginx.conf /etc/nginx/conf.d/frappe-bench.conf
    ```

2.  **Create/Update Nginx Configuration:**

    Create or overwrite the Nginx configuration for the site.

    ```bash
    cat > /home/<USER>/frappe-bench/config/nginx.conf << 'EOF'
    upstream frappe-bench-frappe {
        server 127.0.0.1:8000 fail_timeout=0;
    }

    upstream frappe-bench-socketio-server {
        server 127.0.0.1:9000 fail_timeout=0;
    }

    server {
        listen 80;
        listen [::]:80;

        server_name office.nhyppadec.gov.ng;

        root /home/<USER>/frappe-bench/sites;

        add_header X-Frame-Options "SAMEORIGIN";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy "same-origin, strict-origin-when-cross-origin";

        location /assets {
            try_files $uri =404;
            add_header Cache-Control "max-age=31536000";
        }

        location ~ ^/protected/(.*) {
            internal;
            try_files /office.nhyppadec.gov.ng/$1 =404;
        }

        location /socket.io {
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header X-Frappe-Site-Name office.nhyppadec.gov.ng;
            proxy_set_header Origin $scheme://$http_host;
            proxy_set_header Host $host;

            proxy_pass http://frappe-bench-socketio-server;
        }

        location / {
            try_files /office.nhyppadec.gov.ng/public/$uri @webserver;
        }

        location @webserver {
            proxy_http_version 1.1;
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Frappe-Site-Name office.nhyppadec.gov.ng;
            proxy_set_header Host $host;
            proxy_set_header X-Use-X-Accel-Redirect True;
            proxy_read_timeout 120;
            proxy_redirect off;

            proxy_pass  http://frappe-bench-frappe;
        }
    }
    EOF
    ```

3.  **Test and Reload Nginx:**

    ```bash
    sudo nginx -t
    sudo systemctl reload nginx
    ```

## 2. Site Creation and Management

1.  **Create the Site:**

    ```bash
    cd /home/<USER>/frappe-bench
    bench new-site office.nhyppadec.gov.ng --admin-password admin --install-app erpnext
    ```

2.  **Set as Default Site:**

    ```bash
    cd /home/<USER>/frappe-bench
    bench use office.nhyppadec.gov.ng
    ```

3.  **Enable Scheduler and Disable Maintenance Mode:**

    ```bash
    cd /home/<USER>/frappe-bench
    bench --site office.nhyppadec.gov.ng enable-scheduler
    bench --site office.nhyppadec.gov.ng set-config maintenance_mode 0
    ```

## 3. SSL Configuration with Certbot

1.  **Obtain SSL Certificate:**

    ```bash
    sudo certbot --nginx -d office.nhyppadec.gov.ng --non-interactive --agree-tos --email <EMAIL>
    ```

2.  **Test and Reload Nginx:**

    ```bash
    sudo nginx -t
    sudo systemctl reload nginx
    ```

3.  **Set Up Automatic Renewal (if not already present):**

    ```bash
    (crontab -l 2>/dev/null; echo '@daily /usr/bin/certbot renew --post-hook "systemctl reload nginx"') | crontab -
    ```

## 4. Permissions and Asset Building

1.  **Build Assets:**

    ```bash
    cd /home/<USER>/frappe-bench
    bench build
    ```

2.  **Fix File Permissions:**

    ```bash
    cd /home/<USER>/frappe-bench
    sudo chown -R frappe:www-data sites/ apps/
    sudo find sites/ apps/ -type d -exec chmod 755 {} \;
    sudo find sites/ apps/ -type f -exec chmod 644 {} \;
    sudo chmod +x /home/<USER>/home/<USER>/frappe-bench
    ```

3.  **Clear Cache:**

    ```bash
    cd /home/<USER>/frappe-bench
    bench clear-cache
    bench clear-website-cache
    ```

4.  **Restart Services:**

    ```bash
    sudo supervisorctl restart all
    ```

## 5. Administrator Password

-   The Administrator password was set to `admin` during site creation. If you need to reset it, use the following command:

    ```bash
    cd /home/<USER>/frappe-bench
    bench --site office.nhyppadec.gov.ng set-admin-password admin
    ```

