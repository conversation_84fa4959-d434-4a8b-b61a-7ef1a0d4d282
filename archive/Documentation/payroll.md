
## Source: https://docs.frappe.io/hr/payroll-setup

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Payroll Setup

Salary is a fixed amount of money or compensation paid to an employee by an employer in return for the work performed.

Payroll is the administration of financial records of employees' salaries, wages, bonuses, net pay, and deductions.

To process Payroll in Frappe HR,

  1. Define [Payroll Period](https://docs.frappe.io/hr/payroll-period.html) (optional)
  2. Define [Income Tax Slab](https://docs.frappe.io/hr/income-tax-slab.html) (optional)
  3. Create Salary Structure with Salary Components (Earnings and Deductions)
  4. Assign Salary Structures to each Employee via Salary Structure Assignment
  5. Generate Salary Slips via [Payroll Entry](https://docs.frappe.io/hr/payroll-entry.html).
  6. Book the Salary in your Accounts.



## **Payroll Period**

[Payroll Period](https://docs.frappe.io/hr/payroll-period.html), in Frappe HR, is a period for which Employees get paid for their occupation with the Company. Payroll period helps you define Tax slabs applicable for the period, making it easier to manage changing laws.

> Note: Configuring Payroll Period is optional if you do not intend to use Flexible Benefits or Tax Slabs

## **Salary Component**

This document allows you to define each Earning and Deduction component which can be used to create a Salary Structure and subsequently create Salary Slip or Additional Salary. You can also configure the type, condition and formula as well as other settings which are discussed below. You should be able to enable various combinations of the following options to configure each component as it fits your Company / Regional policies.

  * Depends on Leave Without Pay: Leave Without Pay (LWP) happens when an Employee runs out of allocated leaves or takes a leave without an approval (via Leave Application). If enabled, Frappe HR will automatically deduct the pay in proportion of LWP days divided by the total working days for the month (based on the Holiday List).



> Note: If you don’t want Frappe HR to manage LWP, don’t turn on this flag in any of the Salary Components * Do not include in total: If this option is enabled, the component wont be added to the total of the Earnings or Deductions of the Salary Slip

#### **Earning**

![Salary Component Earnings](https://frappehr.com/files/salary-component.png)

  * Is Additional Component: This option specify that the component can only be paid as Additional Salary. Examples of this component could be Performance Bonus or pay received for on-site deputation etc. Such components are not considered to be part of normal Salary Structure. Instead, Additional Salary with these components can be submitted as required which will be added to the Salary Slip automatically.
  * Is Tax Applicable: If a component needs to be considered for Tax calculations specified as per the Payroll Period you may want to enable this option. It would be required that you have a Payroll Period and Income Tax Slab configured with valid Tax Slabs for payroll processing.
  * Is Payable: Such components can be booked against separate payable accounts and the Accounts shall be configured in the Accounts table
  * Flexible Benefits: Flexible Benefits are earning components which Employees can choose to receive on a pro-rata basis or annually when they claim for. These are mostly tax exempted, unless the Employee fail to file the claim with adequate bills / documents. If turned on, you can specify the maximum benefit allowed for an employee in a year. Employees can create [Employee Benefit Application](https://docs.frappe.io/hr/employee-benefit-application) with the ones they opt for.



> Note: Employee Benefit Application will only allow Employees to only choose from the flexible components which are present in the Salary Structure assigned to the Employee
    
    
    + Pay Against Benefit Claim: Employees can opt to receive flexible benefits annually via Employee Benefit Claim or along with their salary every month. If you enable this, the amount allocated for the component will be paid as the Employee submits an [Employee Benefit Claim](https://docs.frappe.io/hr/employee-benefit-claim.html). Else the amount will be dispersed as part of the Employee's salary on a pro-rata basis.
    
    
        - Only Tax Impact (Cannot Claim But Part of Taxable Income): Such components are those which the company has already paid to the Employee in cash or by some other means, for example a car purchased for the Employee's use. The Employee cannot claim but is liable to pay tax. The amount allocated for this component will be considered while calculating the taxable income of the Employee.
        - Create Separate Payment Entry Against Benefit Claim: Some of the flexible benefits may be legally required to be paid via separate vouchers. If you enable this, while posting the bank entry the amount paid for such components will be posted as a separate entry for each Employee.![Flexible Salary Component](https://frappehr.com/files/salary-component-1.png)  
    
    
    > Note: Normal Tax calculation does not include Flexible Benefits as in most cases these are exempted from Tax. To tax these components anytime before that last payroll, use "Deduct Tax For Unclaimed Employee Benefits" in Payroll Entry / Salary Slip while processing the Salary.
    

#### **Deduction**

![Salary Component Deduction](https://frappehr.com/files/salary-component-2.png)

  * Variable Based On Taxable Salary: If you enable this, the component will be considered as the standard Tax deduction component. Tax will be calculated based on the Income Tax Slab linked to the employee.



## **Salary Structure**

Salary Structure represents how Salaries are structured and calculated based on Earnings and Deductions. Salary structures are used to help organizations:

  1. Maintain pay levels that are competitive with the external labor market,
  2. Maintain internal pay relationships among jobs,
  3. Recognize and reward differences in the level of responsibility, skill, and performance, and manage pay expenditures.



Usual components of a salary structure (in India) include:

  * Basic Salary: It is the taxable base income and generally not more than 40% of CTC.
  * House Rent Allowance: The HRA constitutes 40 to 50% of the basic salary.
  * Special Allowances: Makes up for the remainder part of the salary, mostly smaller than the basic salary which is completely taxable.
  * Leave Travel Allowance: The non-taxable amount paid by the employer to the employee for vacation/trips with family within India.
  * Gratuity: It is basically a lump sum amount paid by the employer when the employee resigns from the organization or retires.
  * PF: Fund collected during emergency or old age. 12% of the basic salary is automatically deducted and goes to the employee provident fund.
  * Medical Allowance: The employer pays the employee for the medical expenditures incurred. It is tax-free up to Rs.15,000.
  * Bonus: Taxable part of the CTC, usually a once a year lump sum amount, given to the employee based on the individual’s as well as the organizational performance for the year.
  * Employee Stock Options: ESOPS are Free/discounted shares given by the company to the employees. This is done to primarily increase employee retention.



![Submitted Salary Structure](https://frappehr.com/files/salary-structure.png) A submitted Salary Structure

### **Creating a New Salary Structure**

To create a new Salary Structure go to:

> Human Resources > Payroll Setup > Salary Structure > New Salary Structure

In the new Salary Structure,

  1. Name the salary Structure and set the company, letterhead for Salary Slip printing and frequency of payroll etc.
  2. Set the starting date from which this is valid (Note: There can only be one Salary Structure that can be “Active” for an Employee during any period).
  3. Configure Leave Encashment Amount per Day which will be the amount payable to Employees on Leave Encashment requests.
  4. Max Benefits amount is the maximum amount eligible as Flexible Components to employees.



#### **Salary Slip Based on Timesheet**

Salary Slip based on Timesheet is applicable if you have timesheet based payroll system

  1. Check "Salary Slip Based on Timesheet"
  2. Select the salary component and enter Hour Rate (Note: This salary component gets added to earnings in Salary Slip)



![Salary Slip based on Timesheet](https://frappehr.com/files/salary-timesheet.png)

#### **Earnings and Deductions in Salary Structure**

In the “Earnings” and “Deductions” tables, you can select the earnings and deductions components The condition and formula configured in Salary Component will be copied by default, but you may change this if required. You may also want to select the Base component in the Earnings table. Note that the amount eligible for each employee should be configured in Salary Structure Assignment.

If the condition and formula for any of the earnings or deductions are not configured in Salary Component, you can calculate the values of Salary Components based on,

#### **Condition and Formula**

![Condition and Formula](https://frappehr.com/files/condition-formula.png)

#### **Condition and Amount**

![Condition and Amount](https://frappehr.com/files/condition-amount.png)

In conditions and formulas,

  * Use field "base" for using base salary of the Employee
  * Use Salary Component abbreviations. For example: BS for Basic Salary
  * Use field name for employee details. For example: Employment Type for employment_type



#### **Account Details**

![Salary Structure Account](https://frappehr.com/files/salary-structure-account.png)

  * Select Mode of Payment and Payment Account for the Salary Slips which will be generated using this Salary Structure



Finally, _Save_ the Salary Structure.

### **Leave Without Pay (LWP)**

Leave Without Pay (LWP) happens when an Employee runs out of allocated leaves or takes a leave without an approval (via Leave Application). If you want Frappe HR to automatically deduct salary in case of LWP, then you must check on the “Apply LWP” column in the Earning Type and Deduction Type masters. The amount of pay cut is the proportion of LWP days divided by the total working days for the month (based on the Holiday List).

If you don’t want Frappe HR to manage LWP, leave the LWP unchecked in all of the Earning Types and Deduction Types.

## **Salary Structure Assignment**

Salary Structure Assignment allows you to assign salary structure and specify the base pay eligible for each employee. It is important that you set the base salary for each assignment as this will be the base salary used for calculations as per the Salary Structure.

To create a new Salary Structure Assignment go to:

> Human Resources > Payroll > Salary Structure Assignment > New Salary Structure Assignment

![Salary Structure Assignment](https://frappehr.com/files/salary-structure-assignment.png)

* * *

# **Processing Payroll**

You can either bulk process payroll for Employees under a department, branch or designation or process payroll individually by creating Salary Slips for each employee.

## **Payroll Processing Using Payroll Entry**

You can also create salary slip for multiple employees using Payroll Entry:

> Human Resources > Payroll > Payroll Entry > New Payroll Entry

#### **Payroll Entry**

![Payroll Entry](https://frappehr.com/files/payroll-entry.png)

In Payroll Entry,

  1. Select the Company for which you want to create the Salary Slips. You can also select the other fields like Branch, Department, Designation or Project to be more specific.
  2. Check _Salary Slip based on Timesheet_ if you want to process timesheet based Salary Slips.
  3. Select the Posting Date and the frequency of payroll which you want to create the Salary Slips.
  4. Click on "Get Employee Details" to get a list of Employees for which the Salary Slips will be created based on the selected criteria.
  5. Enter the Start and End dates for the payroll period.
  6. You can check _Deduct Tax For Unclaimed Employee Benefits_ if you want to deduct taxes for all benefits (Salary Components which are _Is Flexible Benefit_) paid to employees till the current payroll
  7. Similarly, _Deduct Tax For Unsubmitted Tax Exemption Proof_ allows you to deduct taxes for the earnings which were exempted in the previous payrolls as declared in [Employee Tax Exemption Declaration](https://docs.frappe.io/hr/employee-tax-exemption-declaration) but the Employee has not submitted sufficient proof [Employee Tax Exemption Proof Submission](https://docs.frappe.io/hr/employee-tax-exemption-proof-submission)
  8. Select the Cost Center and Payment Account.
  9. Save the form and Submit it to create Salary Slip records for each active Employee for the time period selected. If the Salary Slips are already created, the system will not create any more Salary Slips. You can also just save the form as Draft and create the Salary Slips later.



![Submitted Payroll Entry](https://frappehr.com/files/created-payroll.png)

Once all Salary Slips are created, you can use _View Salary Slips_ to verify if they are created correctly or edit it if you want to deduct Leave Without Pay (LWP).

After checking, you can "Submit" them all together by clicking on "Submit Salary Slip".

> Note: Submitting Salary Slips will also book the default Payroll Payable account to record the accrual of salary.

#### **Booking Salaries in Accounts**

The final step is to book the Salaries in your Accounts.

Salaries in businesses are usually dealt with extreme privacy. In most cases, the companies issues a single payment to the bank combining all salaries and the bank distributes the salaries to each employee’s salary account. This way there is only one payment entry in the company’s books of accounts and anyone with access to the company’s accounts will not have access to the individual salaries.

The salary payment entry is a Journal Entry that debits the total of the earning type salary component and credits the total of deduction type salary component of all Employees to the default account set at Salary Component level for each component.

To generate your salary payment voucher from Payroll Entry, click on - > Make > Bank Entry

![Payroll Make Entry](https://frappehr.com/files/payroll-make-bank-entry.png)

Payroll Entry will route you to Journal Entry with relevant filters to view the draft Journal Vouchers created. You shall set reference number and date for the transactions and Submit the Journal Entries.

> Note: For Salary Components which are Flexible Benefits and has _Create Separate Payment Entry Against Benefit Claim_ checked, Frappe HR will book separate draft Journal Entries.

![Payroll Entry](https://frappehr.com/files/payroll-journal-entry.png)

## **Creating Salary Slips Manually**

Once the Salary Structure is created and assigned to employees via Salary Structure Assignment, you can make a Salary Slip individually. Go to:

> Human Resources > Payroll > Salary Slip > New Salary Slip

#### **Salary Slip**

![Salary Slip](https://frappehr.com/files/salary-slip.png)

##### Payroll Setup

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/payroll-management

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Payroll Management

Payroll processing is an important function of every enterprise HR. Frappe HR greatly simplifies this process by offering an array of features that you can utilize from Salary Structure management to bulk processing Payroll of employees. Read the following documentation to understand how to configure and use Frappe HR Human Resources to super power your Payroll processing.

## **Related Topics**

  1. [Payroll Period](https://docs.frappe.io/hr/payroll-period)
  2. [Salary Component](https://docs.frappe.io/hr/salary-component)
  3. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  4. [Salary Structure Assignment](https://docs.frappe.io/hr/salary-structure-assignment)
  5. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)
  6. [Additional Salary](https://docs.frappe.io/hr/additional-salary)
  7. [Retention Bonus](https://docs.frappe.io/hr/retention-bonus)
  8. [Employee Incentive](https://docs.frappe.io/hr/employee-incentive)



##### Payroll Management

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/payroll-period

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Payroll Period

**A Payroll Period is a period for which Employees get paid for their occupation with the Company.**

Payroll Period helps you define Salary Structures and to calculate tax for a specific period based on applicable Income Tax Slab.

To access Payroll Period, go to:

> **Home > Human Resources > Payroll > Payroll Period**

## **1\. How to create a Payroll Period**

  1. Go to Payroll Period list, click on New.
  2. Enter Name.
  3. Select Start Date and End Date of Payroll Period.
  4. Save.



## **2\. Related Topics**

  1. [Salary Component](https://docs.frappe.io/hr/salary-component)
  2. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  3. [Income Tax Slab](https://docs.frappe.io/hr/income-tax-slab)
  4. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)
  5. [Employee Tax Exemption Proof Submission](https://docs.frappe.io/hr/employee-tax-exemption-proof-submission)
  6. [Employee Tax Exemption Declaration](https://docs.frappe.io/hr/employee-tax-exemption-declaration)



##### Payroll Period

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/salary-component

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Salary Component

**Salaries are paid by organizations to their employees in exchange for the services rendered by them. The different components that make up the Salary Structure are called as Salary Components.**

Salary paid to the employees comprises of several different components, such as basic salary, allowances, arrears, etc. Frappe HR allows you to define these Salary Components and also specify its various attributes.

To access Salary Component, go to: > Home > Human Resources > Payroll > Salary Component

## **1\. How to create a Salary Component**

To create a new Salary Component:

  1. Go to Salary Component list, click on New.
  2. Enter its Name and Abbreviation.
  3. Enter Description of the Salary Component (optional).
  4. Enter the Company name and the Default Account of the Salary Component in the Accounts table.
  5. Save.



![](https://frappehr.com/files/of1TI53.png)

## **2\. Features**

Apart from the above mentioned mandatory fields, some of the additional features of the Salary Component are given below:

### **2.1 Condition and Formula**

In this section, the Condition and Formula required for the calculation of the Salary Component can be specified. To specify the formula, enable the 'Amount based on formula' checkbox.

![](https://frappehr.com/files/QoxdTZF.png)

You can sync updated Condition and Formula values of a Salary Component with existing Salary Structures, where the Component is being used, with the Update Salary Structures button.

![](https://frappehr.com/files/sgRLa11.png)

In case the Salary Component is based on a pre-defined amount, Frappe HR allows you to directly enter the amount in the Amount field (disable the 'Amount based on formula' checkbox).

You can also use some mathematical/date functions while writing formulae.
    
    
    # Consider a component `basic` with amount as 1220.32 as an example:
    
    # int - cast the amount as int
    int(basic) # evaluates to 1220
    
    # flt - cast the amount as flt
    flt(basic, 1) # evaluates to 1220.3
    
    # round - rounds the amount (Banker's Rounding)
    round(basic) # evaluates to 1220
    
    # rounded - rounds the amount based on System Settings or passed method (Banker's Rounding or Commercial Rounding)
    # If basic is 1220.5
    round(1220.5) # evaluates to 1220
    rounded(1220.5, rounding_method="Banker's Rounding") # evaluates to 1220
    rounded(1220.5, rounding_method="Commercial Rounding") # evaluates to 1221
    
    # ceil - rounds the number up to the nearest integer
    ceil(basic) # evaluates to 1221
    
    # floor - rounds the number down to the nearest integer
    floor(basic) # evaluates to 1220
    
    # getdate/date - casts the value `start_date` to a `datetime.date` object
    # eg: Professional Tax is 300 in February and 200 in every other month. `start_date` takes up the value of salary slip's `start_date`
    # In that case the condition can be written as given below:
    
    300 if getdate(start_date).month == 2 else 200
    
    
    
    

> **Note:** This above setup is optional. You can define Amount and Formula/Condition for a Salary Component directly in the Salary Structure also. If they are specified in the Salary Component document itself, the information will be directly fetched in the Salary Structure when the Component is selected.

### **2.2 Additional Properties**

Some of the additional attributes of the Salary Component that can be enabled using checkboxes are as follows:

  * **Is Payable:** Select this if the Salary Component is payable.
  * **Depends on Payment Days:** If this checkbox is enabled then the Salary Component will be calculated based on the number of working days.
  * **Is Tax Applicable:** This checkbox is applicable for Earning Components. Selecting this checkbox allows tax to be applied on this Salary Component.
  * **Deduct Full Tax on Selected Payroll Date:** If checked and the component is used in Additional Salary, the tax amount applicable on the additional amount will be deducted on the specific payroll month. If not checked, the tax will be distributed over the remaining months of the payroll period. For example, if a bonus is given in a particular month using Additional Salary, then you can deduct full tax amount in the same month only.
  * **Round to the Nearest Integer:** Selecting this checkbox allows you to round the amount of this Salary Component to the nearest integer.
  * **Statistical Component:** If selected, the value specified or calculated in this component will not contribute to the earnings or deductions. However, it's value can be referenced by other components that can be added or deducted. If you set a Salary Component as a Statistical component, then you do not have to set the Default Account for the same. Also, you would not be able to set this component as a Flexible Benefit.
  * **Do Not Include in Total:** Selecting this checkbox ensures that the Salary Component is not included in the Total Salary. It is used to define the component which is part of the CTC but not payable (e.g. Usage of Company Cars).
  * **Variable Based On Taxable Salary:** The component is calculated automatically on taxable income based on applicable Income Tax Slab (e.g. TDS or Income Tax).
  * **Exempted from Income Tax:** If checked, the full amount will be deducted from taxable income before calculating income tax without any [declaration](https://docs.frappe.io/hr/employee-tax-exemption-declaration) or [proof submission](https://docs.frappe.io/hr/employee-tax-exemption-proof-submission). For example, Professional Tax in India is deducted from taxable income before calculating income tax.
  * **Disabled:** This checkbox can be selected to disable this Salary Component. A disabled Salary Component cannot be used in the Salary Structure.



### **2.3 Flexible Benefits**

This section is shown if the Salary Component is an Earning Component. Flexible Benefit plans allow employees to avail the benefits they want or need from a package of programs offered by an employer. They may include health insurance, pension plans, telephone expenses, etc. To set a Salary Component as a Flexible Benefit, check the 'Is Flexible Benefit' checkbox.

![](https://frappehr.com/files/3lypNxJ.png)

Enter the maximum yearly amount for this flexible benefit in the 'Max Benefit Amount (Yearly)' field. Some of the additional attributes of the Flexible Benefits that can be enabled using checkboxes are as follows:

  * **Pay Against Benefit Claim:** Enable this checkbox if you want to pay this benefit via the [Employee Benefit Claim](https://docs.frappe.io/hr/employee-benefit-claim).
  * **Only Tax Impact (Cannot Claim But Part of Taxable Income):** If set, the flexible benefit will be part of taxable income.
  * **Create Separate Payment Entry Against Benefit Claim:** If this checkbox is checked, it will let you create a separate payment entry against the Benefit Claim.



## **3\. Related Topics**

  1. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  2. [Salary Structure Assignment](https://docs.frappe.io/hr/salary-structure-assignment)
  3. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)
  4. [Payroll Period](https://docs.frappe.io/hr/payroll-period)



##### Salary Component

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/salary-structure

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Salary Structure

**Salary Structure is the details of the salary being offered to an Employee, in terms of the breakup of the different components constituting the compensation.**

Any changes to the Salary Structure i.e. among the components can have a major impact on what the Employee does, such as the kind of tax exemptions claimed.

Frappe HR allows you to define the Earnings and Deductions of a Salary Structure, Payroll frequency, and Payment Mode among other features.

To access Salary Structure, go to:

> **Home > Human Resources > Payroll > Salary Structure**

## **1\. Prerequisites**

Before you create a Salary Structure, it is advisable you have the following:

  * [Salary Component](https://docs.frappe.io/hr/salary-component)



## **2\. How to create a Salary Structure**

  1. Go to the Salary Structure list, click on New.
  2. Enter the Salary Structure Name.
  3. Select the Company Name and Payroll Frequency.
  4. Save and Submit.



## **2\. Features**

### **2.1 Earnings and Deductions**

Earnings specify the Salary Components that are earned by an Employee. These components typically include basic, allowances, bonuses, and incentives that are added to the employee's Total Salary. On the other hand, Deductions specify the Salary Components that are deducted from the employee's Total Salary. These typically include the taxes.

> **Note: Only Salary Components set as 'Earnings' will be shown in the Earnings table and components set as 'Deductions' will be shown in the Deductions table.**

To create Earnings and Deductions, select the Salary Component in the Component column. Enter the Formula/Condition if not previously specified while creating the [Salary Component](https://docs.frappe.io/hr/salary-component). Additionally, you can also enter a pre-defined amount in the Amount column.

![Salary Structure](https://frappehr.com/files/salary-structure.png)

> **Note: Make sure to click on the downward arrow and enable the 'Amount based on formula' checkbox in case the Salary Component is calculated using a formula.**

### **2.2 Account**

In this section, the [Mode of Payment](https://docs.erpnext.com/docs/v14/user/manual/en/accounts/articles/mode-of-payment) and the [Payment Account](https://docs.erpnext.com/docs/v14/user/manual/en/accounts/chart-of-accounts) that is used to pay the salary can be specified.

### **2.3 Salary Structure for Salary based on Timesheets**

In Frappe HR you can also define the Salary Structure for Salary Slip based on Timesheet, which allows the Company to pay there Employee as per working hours.

Steps for creating Salary Structure based on Timesheets:

  1. Go to Salary Structure List, click on New.
  2. Select checkbox **Salary Slip Based on Timesheet**.
  3. Select the Salary Component.
  4. Enter the Hour Rate. Based on the Rate entered, the amount for Working hours for the selected Salary Component will be calculated accordingly.
  5. Save and Submit.



![Create Salary Slip based on Timesheets](https://frappehr.com/files/salary-structure-for-salary-based-on-timesheets.png)

### **2.4 Leave Encashment Amount Per Day**

In case there are encashable leaves for an Employee, you can define the leave encashment amount per day in this field for this particular Salary Structure. Based on the 'Earning Component' set in the encashed [Leave Type](https://docs.frappe.io/hr/leave-type) and the amount per day, the value for the Salary component will be calculated accordingly in the Salary Slip.

### **2.5 Max Benefits (Amount)**

In this field, the Max Benefits Amount for the Salary Structure can be specified. If this field is filled, make sure the Salary Structure has a [Salary Component](https://docs.frappe.io/hr/salary-component) with the "Is Flexible Benefits" checked, against which this amount will be paid.

Once all the information is saved and submitted, you can assign the Salary Structure by clicking on the Create button and selecting 'Single Assignment' to assign a single employee or 'Bulk Assignments' to assign multiple employees via the [Salary Structure Assignment Tool](https://docs.frappe.io/hr/salary-structure-assignment-tool).

You can also preview the associated Salary Slip by clicking on the Actions button and selecting 'Preview Salary Slip'.

## **3\. Related Topics**

  1. [Salary Component](https://docs.frappe.io/hr/salary-component)
  2. [Salary Structure Assignment](https://docs.frappe.io/hr/salary-structure-assignment)
  3. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)



##### Salary Structure

administrator edited 9 months ago

×

**Salary Structure is the details of the salary being offered to an Employee, in terms of the breakup of the different components constituting the compensation.**

Any changes to the Salary Structure i.e. among the components can have a major impact on what the Employee does, such as the kind of tax exemptions claimed.

Frappe HR allows you to define the Earnings and Deductions of a Salary Structure, Payroll frequency, and Payment Mode among other features.

To access Salary Structure, go to:

> **Home > Human Resources > Payroll > Salary Structure**

## **1\. Prerequisites**

Before you create a Salary Structure, it is advisable you have the following:

  * [Salary Component](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-component)




## **2\. How to create a Salary Structure**

  1. Go to the Salary Structure list, click on New.

  2. Enter the Salary Structure Name.

  3. Select the Company Name and Payroll Frequency.

  4. Save and Submit.




## **2\. Features**

### **2.1 Earnings and Deductions**

Earnings specify the Salary Components that are earned by an Employee. These components typically include basic, allowances, bonuses, and incentives that are added to the employee's Total Salary. On the other hand, Deductions specify the Salary Components that are deducted from the employee's Total Salary. These typically include the taxes.

> **Note: Only Salary Components set as 'Earnings' will be shown in the Earnings table and components set as 'Deductions' will be shown in the Deductions table.**

To create Earnings and Deductions, select the Salary Component in the Component column. Enter the Formula/Condition if not previously specified while creating the [Salary Component](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-component). Additionally, you can also enter a pre-defined amount in the Amount column.

![Salary Structure](https://frappehr.com/files/salary-structure.png)  


> **Note: Make sure to click on the downward arrow and enable the 'Amount based on formula' checkbox in case the Salary Component is calculated using a formula.**

### **2.2 Account**

In this section, the [Mode of Payment](https://docs.erpnext.com/docs/v14/user/manual/en/accounts/articles/mode_of_payment) and the [Payment Account](https://docs.erpnext.com/docs/v14/user/manual/en/accounts/chart-of-accounts) that is used to pay the salary can be specified.

### **2.3 Salary Structure for Salary based on Timesheets**

In Frappe HR you can also define the Salary Structure for Salary Slip based on Timesheet, which allows the Company to pay there Employee as per working hours.

Steps for creating Salary Structure based on Timesheets:

  1. Go to Salary Structure List, click on New.

  2. Select checkbox **Salary Slip Based on Timesheet**.

  3. Select the Salary Component.

  4. Enter the Hour Rate. Based on the Rate entered, the amount for Working hours for the selected Salary Component will be calculated accordingly.

  5. Save and Submit.

![Create Salary Slip based on Timesheets](https://frappehr.com/files/salary-structure-for-salary-based-on-timesheets.png)  





### **2.4 Leave Encashment Amount Per Day**

In case there are encashable leaves for an Employee, you can define the leave encashment amount per day in this field for this particular Salary Structure. Based on the 'Earning Component' set in the encashed [Leave Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-type) and the amount per day, the value for the Salary component will be calculated accordingly in the Salary Slip.

### **2.5 Max Benefits (Amount)**

In this field, the Max Benefits Amount for the Salary Structure can be specified. If this field is filled, make sure the Salary Structure has a [Salary Component](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-component) with the "Is Flexible Benefits" checked, against which this amount will be paid.

Once all the information is saved and submitted, you can assign the Salary Structure by clicking on the Create button and selecting 'Single Assignment' to assign a single employee or 'Bulk Assignments' to assign multiple employees via the [Salary Structure Assignment Tool](https://frappehr.com/docs/v14/en/salary-structure-assignment-tool).

You can also preview the associated Salary Slip by clicking on the Actions button and selecting 'Preview Salary Slip'.

## **3\. Related Topics**

  1. [Salary Component](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-component)

  2. [Salary Structure Assignment](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure-assignment)

  3. [Payroll Entry](https://frappehr.com/docs/v14/user/manual/en/human-resources/payroll-entry)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/salary-structure-assignment

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Salary Structure Assignment

**Salary Structure Assignment form allows you to assign a particular Salary Structure to the employee.**

In Frappe HR, you can create multiple Salary Structure Assignments for the same Employee for different periods.

To access Salary Structure Assignment, go to: > Home > Human Resources > Payroll > Salary Structure Assignment

## **1\. Prerequisites**

Before you create a Salary Structure Assignment, it is advisable you have the following documents:

  1. [Employee](https://docs.frappe.io/hr/employee)
  2. [Salary Component](https://docs.frappe.io/hr/salary-component)
  3. [Salary Structure](https://docs.frappe.io/hr/salary-structure)



## **2\. How to create a Salary Structure Assignment:**

  1. Go to Salary Structure Assignment list and click on New.
  2. Select the Employee and Salary Structure.
  3. Select the From Date from which this particular Salary Structure will be applicable.
  4. Select preferred Income Tax Slab for the employee.
  5. Enter Base and Variable amount as per requirement. Base amount refers to the Base Salary of the Employee, which is fixed and paid out, regardless of employees meeting their goals. Variable pay, on the other hand, is the portion of sales compensation determined by employee performance. When employees hit their goals (aka quota), variable pay is provided as a type of bonus, incentive pay, or commission.



![Salary Structure Assignment](https://frappehr.com/files/salary-structure-assignment.png)

### **3.1 Bulk Salary Structure Assignment**

Frappe HR also allows creating Salary Structure Assignments for multiple employees at once.

  1. Go to the Salary Structure list.
  2. Click on Bulk Salary Structure Assignment.
  3. This will take you to the [Salary Structure Assignment Tool](https://docs.frappe.io/hr/salary-structure-assignment-tool), where you can assign Salary Structures in bulk.



![](https://frappehr.com/files/STjQmpp.png)

> **For version 14, follow the steps below.**

You can assign a Salary Structure to Employee(s) directly through the Salary Structure document. To assign the Salary Structure to a single employee, click on the 'Assign Salary Structure' button in the Salary Structure document.

![Salary Structure Assignment](https://frappehr.com/files/assign-sal1.png)

If you want to bulk assign the Salary Structure to multiple employees, you can do so via the 'Assign to Employees' button.

![Salary Structure Assignment](https://frappehr.com/files/assign-sal2.png)

You can optionally filter out employees based on Employee Grade, Department, Designation, and Employee itself.

![Salary Structure Assignment](https://frappehr.com/files/assign-sal3.png)

Once this is done, click on the 'Assign' button to assign the Salary Structure accordingly.

## **4\. Related Topics**

  1. [Salary Component](https://docs.frappe.io/hr/salary-component)
  2. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  3. [Employee Grade](https://docs.frappe.io/hr/employee-grade)
  4. [Department](https://docs.frappe.io/hr/department)
  5. [Designation](https://docs.frappe.io/hr/designation)
  6. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)



##### Salary Structure Assignment

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/salary-structure-assignment-tool

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Salary Structure Assignment Tool

The Salary Structure Assignment Tool allows you to assign Salary Structures to employees in bulk.

> Home > Human Resources > Payroll > Salary Structure Assignment Tool

## **1\. Prerequisites**

Before using the Salary Structure Assignment Tool, it is advisable to create the following documents:

  * [Employee](/hr/employee)

  * [Salary Structure](/hr/salary-structure)




## **2\. How to assign Salary Structures using the Salary Structure Assignment Tool**

  1. Select a Company if it hasn't already been selected. This should also fetch its default Payroll Payable Account.

  2. Select the Salary Structure that you wish to assign. This will fetch the Currency associated with it.

  3. Select From Date which is the date from which you wish to assign the Salary Structure. This will cause all employees, that do not already have a Salary Structure Assignment on that date, to be loaded in the Select Employees table below.

  4. Select an Income Tax Slab if applicable.

  5. Use Quick Filters for filtering employees based on specific fields, or add additional custom filters through Advanced Filters.

  6. Select suitable employees from the Select Employees table below.

  7. You can update the Base and Variable amounts for an employee by double clicking and editing the required cell. If you wish to update these values in bulk, click on the Update button, select the appropriate field, and enter the desired amount. Click on Update.

  8. If everything looks right, click on the Assign Structure button.




![](https://frappehr.com/files/pnlAEli.png)

[Salary Structure Assignments](https://docs.frappe.io/hr/salary-structure-assignment) made using this tool will now be found in their list:

![](https://frappehr.com/files/ZZ01jWY.png)

## **3\. Related Topics**

  1. [Payroll Period](/hr/payroll-period)

  2. [Income Tax Slab](/hr/income-tax-slab)

  3. [Salary Component](/hr/salary-component)

  4. [Salary Slip](/hr/salary-slip)

  5. [Payroll Entry](/hr/payroll-entry)




##### Salary Structure Assignment Tool

administrator edited 9 months ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/salary-slip

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Salary Slip

**A salary slip is a document issued to an employee. It contains a detailed description of the employee’s salary components and amounts.**

To access Salary Slip, go to: > Home > Human Resources > Payroll > Salary Slip

## **1\. Prerequisites**

Before creating Salary Slip, it is advised that you create the following first:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  * [Salary Structure Assignment](https://docs.frappe.io/hr/salary-structure-assignment)



## **2\. How to create a Salary Slip**

  1. Go to Salary Slip, Click on New.
  2. Select Employee. On selecting Employee all details of the Employee will be fetched from Salary Structure which is assigned to that Employee. This includes details such as Payroll Frequency, Earnings, Deductions, etc.
  3. Select Start Date and End Date.
  4. Save.



## **3\. Feature**

### **3.1. Salary Slip based on Attendance/Leave**

HR users can create Salary Slip based on Attendance or leave. The Working days will calculated on basis of leave/Attendance, depending on the field **Calculate Payroll Working Days Based On** in [HR Settings](https://docs.frappe.io/hr/hr-settings). If Payroll is based on Attendance then, the **Leave without pay** will be considered as absent and **half-day** will be considered as half-day absent.

### **3.2. Salary Slip based on Timesheet**

For creating Salary Slip based on timesheet you need to create Salary Structure for Timesheets.

Frappe HR also provides an option to create Salary slip based on working hours based on [Timesheet](https://docs.erpnext.com/docs/user/manual/en/timesheets). You can create Salary Slip after submitting the Timesheet by clicking directly on **Create Salary Slip** button on the top right.

![Create Salary Slip based on Timesheets](https://frappehr.com/files/create-salary-slip-based-on-timesheets.png)

The Payment Amount is calculated based on Hour Rate defined in Salary Structure and is reflected in the Earnings table.

### **3.3 Year to Date and Month to Date**

For every salary slip, 'Year to Date' and 'Month to Date' are computed.

![Year to Date and Month to Date](https://frappehr.com/files/ytd-and-mtd.png)

  * **Year to Date** : Total salary booked for that particular employee from the beginning of the year (payroll period or fiscal year) up to the current salary slip's end date.
  * **Month to Date** : Total salary booked for a particular employee from the beginning of the month (for which the payroll entry is created) up to the current salary slip's end date.



Year to Date is also computed for every component in the earnings and deduction tables. The "Salary Slip with Year to Date" print format is available with Year to Date and Month to Date computations.

![Year to Date for Salary Slip Components](https://frappehr.com/files/ytd-component.png)

### **3.4 Bulk Email Salary Slips**

By default, the Payroll Entry sends salary slip emails to all the employees on salary slip submission if **Email Salary Slip to Employee** is enabled in Payroll Settings. Optionally, you can also set up the sender email account and email template for this.

![](https://frappehr.com/files/X8WK2C7.png)

But if there are a few employees who don't have an email ID set or the setup is incorrect during this action, you can use the bulk action in the Salary Slip list view to trigger sending emails to selected employees later.

![bulk](https://frappehr.com/files/bulk.png)

##### Salary Slip

administrator edited 9 months ago

×

**A salary slip is a document issued to an employee. It contains a detailed description of the employee’s salary components and amounts.**

To access Salary Slip, go to: > Home > Human Resources > Payroll > Salary Slip

## **1\. Prerequisites**

Before creating Salary Slip, it is advised that you create the following first:

  * [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee)

  * [Salary Structure](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure)

  * [Salary Structure Assignment](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure-assignment)




## **2\. How to create a Salary Slip**

  1. Go to Salary Slip, Click on New.

  2. Select Employee. On selecting Employee all details of the Employee will be fetched from Salary Structure which is assigned to that Employee. This includes details such as Payroll Frequency, Earnings, Deductions, etc.

  3. Select Start Date and End Date.

  4. Save.




## **3\. Feature**

### **3.1. Salary Slip based on Attendance/Leave**

HR users can create Salary Slip based on Attendance or leave. The Working days will calculated on basis of leave/Attendance, depending on the field **Calculate Payroll Working Days Based On** in [HR Settings](https://frappehr.com/docs/v14/user/manual/en/human-resources/hr-settings). If Payroll is based on Attendance then, the **Leave without pay** will be considered as absent and **half-day** will be considered as half-day absent.

### **3.2. Salary Slip based on Timesheet**

For creating Salary Slip based on timesheet you need to create Salary Structure for Timesheets.

Frappe HR also provides an option to create Salary slip based on working hours based on [Timesheet](https://frappehr.com/docs/v14/user/manual/en/projects/timesheets). You can create Salary Slip after submitting the Timesheet by clicking directly on **Create Salary Slip** button on the top right.

![Create Salary Slip based on Timesheets](https://frappehr.com/files/create-salary-slip-based-on-timesheets.png)  


The Payment Amount is calculated based on Hour Rate defined in Salary Structure and is reflected in the Earnings table.

### **3.3 Year to Date and Month to Date**

For every salary slip, 'Year to Date' and 'Month to Date' are computed.

![Year to Date and Month to Date](https://frappehr.com/files/ytd-and-mtd.png)  


  * **Year to Date** : Total salary booked for that particular employee from the beginning of the year (payroll period or fiscal year) up to the current salary slip's end date.

  * **Month to Date** : Total salary booked for a particular employee from the beginning of the month (for which the payroll entry is created) up to the current salary slip's end date.




Year to Date is also computed for every component in the earnings and deduction tables. The "Salary Slip with Year to Date" print format is available with Year to Date and Month to Date computations.

![Year to Date for Salary Slip Components](https://frappehr.com/files/ytd-component.png)  


### **3.4 Bulk Email Salary Slips**

By default, the Payroll Entry sends salary slip emails to all the employees on salary slip submission if **Email Salary Slip to Employee** is enabled in Payroll Settings. Optionally, you can also set up the sender email account and email template for this.

![](https://frappehr.com/files/X8WK2C7.png)  


But if there are a few employees who don't have an email ID set or the setup is incorrect during this action, you can use the bulk action in the Salary Slip list view to trigger sending emails to selected employees later.

![bulk](https://frappehr.com/files/bulk.png)  


  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/payroll-entry

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Payroll Entry

**Payroll is the sum total of all compensation a business must pay to its employees for a set period of time or on a given date.**

In Frappe HR, Payroll Entry enables bulk processing of payroll for employees. In other words, processing salary slips of all employees in one go. The bulk processing can be Company-wide or based on these categories: Branch, Department, or Designation.

To access Payroll Entry, go to:

> **Home > Human Resources > Payroll > Payroll Entry**

## **1\. How to create a Payroll Entry**

  1. Go to to Payroll Entry list, click on New.
  2. Select the Payroll Frequency.
  3. Select Branch, Designation and Department to filter out employees (optional).
  4. Select Project (optional) if you want to run the payroll against a project.
  5. Select 'Validate Attendance' and 'Salary Slip Based on Timesheet' checkboxes in case you want to deduct the salary based on attendance and if you want to also consider the timesheets of the employees respectively.
  6. Select the Payment Account to make the Bank Entry.
  7. Save.



Once the information is saved, click on the **Get Employees** button to get a list of Employees for which the Salary Slips will be created based on the selected criteria.

Once the list of Employees is fetched, click on the **Create Salary Slips** button to generate Salary Slips.

![Payroll Entry](https://frappehr.com/files/payroll-entry-get-employees.png)

> **Note: If the Salary Slips are already created, the system will not create any more Salary Slips. You can also just save the form as Draft and create the Salary Slips later.**

## **2\. Features**

### **2.1 Salary Accrual**

After verifying the Salary Slips, you can Submit them all together by clicking on the **Submit Salary Slip** button.

![Payroll Entry](https://frappehr.com/files/payroll-entry.png)

This will also book the default Payroll Payable account against respective Expense Heads (as configured in Salary Components) to record the accrual of salary to employees.

**Cost Center:**

You can select Cost Center in the Payroll Entry against which the expenses will be posted.

If you want to book expenses against multiple cost centers based on Employee/Department, you can do so by setting Payroll Cost Center in the Employee/Department master.

![](https://frappehr.com/files/ViFAtRT.png)

Even multiple cost centers can be assigned against a single Employee. You can do so via the Salary Structure Assignment document.

![](https://frappehr.com/files/hUnTWPJ.png)

Cost Center assigned in Salary Structure Assignment gets the highest priority and then Employee and Department master respectively. The least priority is given to the Cost Center selected in Payroll Entry.

![Payroll Entry](https://frappehr.com/files/payroll-make-accrual-entry.png)

> **Note: Submitting Salary Slips one by one manually will not create the Journal Entry to record salary accrual.**

### **2.2 Salary Payment**

The final step is to book the Salary Payment.

Salaries in businesses are usually dealt with extreme privacy. In most cases, companies issue a single payment to the bank combining all salaries and the bank distributes the salaries to each employee’s salary account.

This way there is only one payment entry in the company’s books of accounts and anyone with access to the company’s accounts will not have access to the individual salaries.

The salary payment entry is a Journal Entry that debits the total of the Earnings type salary component and credits the total of Deductions type salary component of all Employees to the default account set at the Salary Component level for each component.

To generate your salary payment voucher from Payroll Entry, click on the **Make Bank Entry** button.

Payroll Entry will route you to Journal Entry with relevant filters to view the draft Journal Vouchers created. You will have to set the reference number and date for the transactions and Submit the Journal Entry.

![Payroll Entry](https://frappehr.com/files/payroll-make-bank-entry.png)

> **Note: For Salary Components which are Flexible Benefits and has _Create Separate Payment Entry Against Benefit Claim_ checked, Frappe HR will book separate draft Journal Entries.**

## **3\. Related Topics**

  1. [Salary Component](https://docs.frappe.io/hr/salary-component)
  2. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  3. [Payroll Period](https://docs.frappe.io/hr/payroll-period)
  4. [Journal Entry](https://docs.erpnext.com/docs/user/manual/en/journal-entry)



##### Payroll Entry

administrator edited 9 months ago

×

**Payroll is the sum total of all compensation a business must pay to its employees for a set period of time or on a given date.**

In Frappe HR, Payroll Entry enables bulk processing of payroll for employees. In other words, processing salary slips of all employees in one go. The bulk processing can be Company-wide or based on these categories: Branch, Department, or Designation.

To access Payroll Entry, go to:

> **Home > Human Resources > Payroll > Payroll Entry**

## **1\. How to create a Payroll Entry**

  1. Go to to Payroll Entry list, click on New.

  2. Select the Payroll Frequency.

  3. Select Branch, Designation and Department to filter out employees (optional).

  4. Select Project (optional) if you want to run the payroll against a project.

  5. Select 'Validate Attendance' and 'Salary Slip Based on Timesheet' checkboxes in case you want to deduct the salary based on attendance and if you want to also consider the timesheets of the employees respectively.

  6. Select the Payment Account to make the Bank Entry.

  7. Save.




Once the information is saved, click on the **Get Employees** button to get a list of Employees for which the Salary Slips will be created based on the selected criteria.

Once the list of Employees is fetched, click on the **Create Salary Slips** button to generate Salary Slips.

![Payroll Entry](https://frappehr.com/files/payroll-entry-get-employees.png)  


> **Note: If the Salary Slips are already created, the system will not create any more Salary Slips. You can also just save the form as Draft and create the Salary Slips later.**

## **2\. Features**

### **2.1 Salary Accrual**

After verifying the Salary Slips, you can Submit them all together by clicking on the **Submit Salary Slip** button.

![Payroll Entry](https://frappehr.com/files/payroll-entry.png)  


This will also book the default Payroll Payable account against respective Expense Heads (as configured in Salary Components) to record the accrual of salary to employees.

**Cost Center:**

You can select Cost Center in the Payroll Entry against which the expenses will be posted.

If you want to book expenses against multiple cost centers based on Employee/Department, you can do so by setting Payroll Cost Center in the Employee/Department master.

![](https://frappehr.com/files/ViFAtRT.png)  


Even multiple cost centers can be assigned against a single Employee. You can do so via the Salary Structure Assignment document.

![](https://frappehr.com/files/hUnTWPJ.png)  


Cost Center assigned in Salary Structure Assignment gets the highest priority and then Employee and Department master respectively. The least priority is given to the Cost Center selected in Payroll Entry.

![Payroll Entry](https://frappehr.com/files/payroll-make-accrual-entry.png)  


> **Note: Submitting Salary Slips one by one manually will not create the Journal Entry to record salary accrual.**

### **2.2 Salary Payment**

The final step is to book the Salary Payment.

Salaries in businesses are usually dealt with extreme privacy. In most cases, companies issue a single payment to the bank combining all salaries and the bank distributes the salaries to each employee’s salary account.

This way there is only one payment entry in the company’s books of accounts and anyone with access to the company’s accounts will not have access to the individual salaries.

The salary payment entry is a Journal Entry that debits the total of the Earnings type salary component and credits the total of Deductions type salary component of all Employees to the default account set at the Salary Component level for each component.

To generate your salary payment voucher from Payroll Entry, click on the **Make Bank Entry** button.

Payroll Entry will route you to Journal Entry with relevant filters to view the draft Journal Vouchers created. You will have to set the reference number and date for the transactions and Submit the Journal Entry.

![Payroll Entry](https://frappehr.com/files/payroll-make-bank-entry.png)  


> **Note: For Salary Components which are Flexible Benefits and has _Create Separate Payment Entry Against Benefit Claim_ checked, Frappe HR will book separate draft Journal Entries.**

## **3\. Related Topics**

  1. [Salary Component](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-component)

  2. [Salary Structure](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure)

  3. [Payroll Period](https://frappehr.com/docs/v14/user/manual/en/human-resources/payroll-period)

  4. [Journal Entry](https://frappehr.com/docs/v14/user/manual/en/accounts/journal-entry)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/retention-bonus

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Retention Bonus

**Retention bonus is a payment or reward outside of an employee's regular salary that is offered as an incentive to keep a key employee on the job.**

Frappe HR allows you to configure Retention Bonus for an Employee for a particular period.

To access Retention Bonus, go to: > Home > Human Resources > Payroll > Retention Bonus

## **1\. Prerequisites**

Before creating a Retention Bonus, it is advisable to create the following:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Salary Component](https://docs.frappe.io/hr/salary-component)



## **2\. How to create a Retention Bonus**

  1. Go to Retention Bonus list, click on New.
  2. Select Employee and Bonus Payment Date.
  3. Enter the Bonus Amount.
  4. Select the [Salary Component](https://docs.frappe.io/hr/salary-component) under which you want to give the bonus.
  5. Save and Submit.
  6. On submit, 'Additional Salary' document of the specified 'Salary Component' is created. This will be fetched while running Payroll Entry.



![Retention Bonus](https://frappehr.com/files/retention-bonus.png)

## **3\. Related Topics**

  1. [Employee Incentive](https://docs.frappe.io/hr/employee-incentive)
  2. [Additional Salary](https://docs.frappe.io/hr/additional-salary)
  3. [Salary Component](https://docs.frappe.io/hr/salary-component)
  4. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  5. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)



##### Retention Bonus

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---

