
## Source: https://www.nhyppadec.gov.ng/background

## Background

The Hydroelectric Power Producing Areas Development Commission was established by an Act of the National Assembly to address ecological challenges from the operations of Hydroelectric Dams. 

### Founded

#### 2010

Hydroelectric Power Producing Areas Development Commission (Establishment) Act was passed and signed.

### Amendment

#### 2013

the act was amended to include Benue and Plateau states and replaces 30% revenue derived from the total revenue accruing to the companies or authorities operating in the host communities with 10% revenue derived from concessioning of the hydro plants and royalties paid to the Federal Government.

### Amendment ll

#### 2016

Amendment of the HYPPADEC Act by reducing the percentage from 30%to 10%,on the total revenue deductible from revenue generated by any company or authority from the operations of any hydroelectric dam, in any member state of the Commission

### Inaugurated

#### 2020

in 2020, President <PERSON><PERSON>, GCFR, constituted Governing Council for the Commission which was inaugurated in December,2020.

![Beautiful Nature](https://static.wixstatic.com/media/11062b_5b16118a43e44e6282c9b300df660aca~mv2.jpg/v1/fill/w_147,h_98,al_c,q_80,usm_0.66_1.00_0.01,blur_2,enc_avif,quality_auto/11062b_5b16118a43e44e6282c9b300df660aca~mv2.jpg)

##### Mission

To harness resources in the most transparent, acceptable and cost-efficient manner towards ameliorating the negative effects of hydroelectric dam operations in HYPPADEC member states.

Vision

To bring equitable and sustainable development that will impact positively on HYPPADEC member states.

Core Responsibility

The Hydroelectric Power Producing Areas Development Commission is charged with the responsibility of formulating policies and guidelines for the development of hydroelectric power producing areas and managing ecological menace due to operations of dams and other hydroelectric power activities.

---


## Source: https://docs.frappe.io/hr/employee

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee

**An individual who works part-time or full-time under a contract of employment, and has recognized rights and duties of your company is your Employee.**

In Frappe HR, you can manage the Employee master. It captures the demographic, personal, and professional details, joining and leave details, etc. of the Employee.

To access the Employee master, go to:

> **Home > Human Resources > Employee**

![](https://frappehr.com/files/RtArfPD.gif)

## **1\. Prerequisites**

Before creating an Employee, it is advised to create the following:

  * [Company](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/company-setup#1-how-to-create-a-new-company)



## **2\. How to create an Employee**

  1. Go to the Employee list, and click on New.
  2. Enter the Employee's personal details such as Name, Gender, Date of Birth, and Date of Joining.
  3. Save.



As shown below, all the mandatory fields are marked with red asterisks.

![](https://frappehr.com/files/rqUdH4m.png)

## **3\. Features**

Apart from the aforementioned mandatory details, some additional details that can be captured in the Employee master are as follows:

### **3.1 Employment Type**

You can set an [Employment Type](https://docs.frappe.io/hr/employment-type) such as Intern, Contract, Full-time, Part-time, Probation, etc. for an Employee.

### **3.2 Create User**

The User ID can be linked to the Employee. In case the User ID is not created, you can click on 'Create a New User' in the dropdown to create one.

By clicking on the 'Create User Permission' checkbox, the Employee's access to other records can be restricted. Check [Adding Users](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/users-and-permissions/adding-users) to learn how to create users and add permissions.

### **3.3 Joining Details**

The Joining Details of the Employee such as the Offer Date, Confirmation Date, Contract End Date, Notice (Days), and Date of Retirement can be captured.

### **3.4 Department and Grade**

In a company, the Employees are usually grouped on the basis of [Department](https://docs.frappe.io/hr/department), [Grade](https://docs.frappe.io/hr/employee-grade), [Designation](https://docs.frappe.io/hr/designation), and [Branch](https://docs.frappe.io/hr/branch).

In the Department and Grade section, these details of the Employee can be saved. In the 'Reports to' field, the person to whom the Employee has to report his duties to can be captured.

### **3.5 Leave Details**

In Leave Details, you can save the [Leave Policy](https://docs.frappe.io/hr/leave-policy) and [Holiday List](https://docs.frappe.io/hr/holiday-list) details. Leave Policy specifies the type and number of leaves an Employee is entitled to, and Holiday List is a list which contains the dates of holidays and weekly offs.

### **3.6 Salary Details**

Here, the mode of salary payment, i.e. through Bank, Cheque or Cash can be selected.

### **3.7 Contact Details**

Employee's Contact information such as Mobile Number, Current and Permanent Address, Personal and Company Email ID can be captured here. In the Preferred Email ID field, either the the Company Email, Personal Email or User ID of the Employee can be selected depending on the user's preference.

### **3.8 Personal Details**

Personal Details of the Employee such as Family Background details like name and occupation of parent, spouse and children, Passport Details including date and place of issue, Health Details like height, weight, allergies, medical concerns, etc. can be saved.

### **3.9 Educational Qualification**

Here, the Educational Details such as School/University, Qualification, Level and Year of Passing of the Employee can be saved as shown below:

![](https://frappehr.com/files/educational-qualification.png)

Additionally, details such as Class/Percentage and Subjects can also be saved by clicking on the downward arrow in the Education table.

### **3.10 Previous Work Experience**

Just like Educational Qualification, an Employee's Previous Work Experience can also be captured in the External Work History table as shown below:

![Previous Work Experience](https://frappehr.com/files/previous-work-experience.png)

Additionally, details such as Contact of the previous company and Total Experience in years can also be saved in the External Work History table.

### **3.11 Exit**

Exit details of the employee (if any) such as Resignation, Exit Interview and [Leave Encashment](https://docs.frappe.io/hr/leave-encashment) details can be saved. When the status of the Employee is set to 'Left', it is mandatory to fill the Relieving Date.

> **Note: Once the Employee status is set to 'Left', that particular Employee master won't be accessible in further transactions.**

### **3.12 Additional Features**

Some additional features included in the Employee master are as follows:

  * Emergency Contact
  * Health Insurance
  * Personal Bio
  * History in the Company



## **3\. Related Topics**

  1. [Leave Management](https://docs.frappe.io/hr/leaves)
  2. [Payroll Management](https://docs.frappe.io/hr/payroll-setup)



##### Employee

administrator edited 9 months ago

×

**An individual who works part-time or full-time under a contract of employment, and has recognized rights and duties of your company is your Employee.**

In Frappe HR, you can manage the Employee master. It captures the demographic, personal, and professional details, joining and leave details, etc. of the Employee.

To access the Employee master, go to:

> **Home > Human Resources > Employee**

![](https://frappehr.com/files/RtArfPD.gif)

## **1\. Prerequisites**

Before creating an Employee, it is advised to create the following:

  * [Company](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/company-setup#1-how-to-create-a-new-company)



## **2\. How to create an Employee**

  1. Go to the Employee list, and click on New.
  2. Enter the Employee's personal details such as Name, Gender, Date of Birth, and Date of Joining.
  3. Save.



As shown below, all the mandatory fields are marked with red asterisks.

![](https://frappehr.com/files/rqUdH4m.png)

## **3\. Features**

Apart from the aforementioned mandatory details, some additional details that can be captured in the Employee master are as follows:

### **3.1 Employment Type**

You can set an [Employment Type](https://docs.frappe.io/hr/employment-type) such as Intern, Contract, Full-time, Part-time, Probation, etc. for an Employee.

### **3.2 Create User**

The User ID can be linked to the Employee. In case the User ID is not created, you can click on 'Create a New User' in the dropdown to create one.

By clicking on the 'Create User Permission' checkbox, the Employee's access to other records can be restricted. Check [Adding Users](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/users-and-permissions/adding-users) to learn how to create users and add permissions.

### **3.3 Joining Details**

The Joining Details of the Employee such as the Offer Date, Confirmation Date, Contract End Date, Notice (Days), and Date of Retirement can be captured.

### **3.4 Department and Grade**

In a company, the Employees are usually grouped on the basis of [Department](https://docs.frappe.io/hr/department), [Grade](https://docs.frappe.io/hr/employee-grade), [Designation](https://docs.frappe.io/hr/designation), and [Branch](https://docs.frappe.io/hr/branch).

In the Department and Grade section, these details of the Employee can be saved. In the 'Reports to' field, the person to whom the Employee has to report his duties to can be captured.

### **3.5 Leave Details**

In Leave Details, you can save the [Leave Policy](https://docs.frappe.io/hr/leave-policy) and [Holiday List](https://docs.frappe.io/hr/holiday-list) details. Leave Policy specifies the type and number of leaves an Employee is entitled to, and Holiday List is a list which contains the dates of holidays and weekly offs.

### **3.6 Salary Details**

Here, the mode of salary payment, i.e. through Bank, Cheque or Cash can be selected.

### **3.7 Contact Details**

Employee's Contact information such as Mobile Number, Current and Permanent Address, Personal and Company Email ID can be captured here. In the Preferred Email ID field, either the the Company Email, Personal Email or User ID of the Employee can be selected depending on the user's preference.

### **3.8 Personal Details**

Personal Details of the Employee such as Family Background details like name and occupation of parent, spouse and children, Passport Details including date and place of issue, Health Details like height, weight, allergies, medical concerns, etc. can be saved.

### **3.9 Educational Qualification**

Here, the Educational Details such as School/University, Qualification, Level and Year of Passing of the Employee can be saved as shown below:

![](https://frappehr.com/files/educational-qualification.png)

Additionally, details such as Class/Percentage and Subjects can also be saved by clicking on the downward arrow in the Education table.

### **3.10 Previous Work Experience**

Just like Educational Qualification, an Employee's Previous Work Experience can also be captured in the External Work History table as shown below:

![Previous Work Experience](https://frappehr.com/files/previous-work-experience.png)

Additionally, details such as Contact of the previous company and Total Experience in years can also be saved in the External Work History table.

### **3.11 Exit**

Exit details of the employee (if any) such as Resignation, Exit Interview and [Leave Encashment](https://docs.frappe.io/hr/leave-encashment) details can be saved. When the status of the Employee is set to 'Left', it is mandatory to fill the Relieving Date.

> **Note: Once the Employee status is set to 'Left', that particular Employee master won't be accessible in further transactions.**

### **3.12 Additional Features**

Some additional features included in the Employee master are as follows:

  * Emergency Contact
  * Health Insurance
  * Personal Bio
  * History in the Company



## **3\. Related Topics**

  1. [Leave Management](https://docs.frappe.io/hr/leaves)
  2. [Payroll Management](https://docs.frappe.io/hr/payroll-intro)



Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employment-type

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employment Type

**You can employ people under a number of categories each having their own wage and leave entitlements. This is called an Employment Type.**

Frappe HR allows you to select an Employment Type from a pre-defined list or even create a new Employment Type based on your requirements.

To access the Employment Type, go to:

> **Home > Human Resources > Employee > Employment Type**

## **1\. How to create an Employment Type**

  1. Go to the Employment Type list, click on New.
  2. Enter the Name of the Employment Type.
  3. Save.



![Employment Type](https://frappehr.com/files/employment-type.png)

The Employment Type can be linked to the [Employee](https://docs.frappe.io/hr/employee) master.

## **2\. Related Topics**

  1. [Employee](https://docs.frappe.io/hr/employee)
  2. [Employment Type](https://docs.frappe.io/hr/employment-type)
  3. [Branch](https://docs.frappe.io/hr/branch)
  4. [Department](https://docs.frappe.io/hr/department)
  5. [Designation](https://docs.frappe.io/hr/designation)
  6. [Employee Grade](https://docs.frappe.io/hr/employee-grade)



##### Employment Type

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/branch

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Branch

**A Branch office is an outlet of a company located at a different location, other than the main office.**

Frappe HR allows you to create and keep a record of the different branches of your organization.

To access Branch, go to:

> **Home > Human Resources > Employee > Branch**

## **1\. Prerequisites**

Before creating a Branch, it is mandatory you create the following documents:

  * [Company](https://docs.erpnext.com/docs/user/manual/en/company-setup)



## **2\. How to create a Branch**

  1. Go to the Branch list, click on New.
  2. Enter the name of the Branch.
  3. Save.



![Branch](https://frappehr.com/files/branch.png)

You can link the Branch to the [Employee](https://docs.frappe.io/hr/employee) master.

## **3\. Related Topics**

  1. [Employee](https://docs.frappe.io/hr/employee)
  2. [Employment Type](https://docs.frappe.io/hr/employment-type)
  3. [Department](https://docs.frappe.io/hr/department)
  4. [Designation](https://docs.frappe.io/hr/designation)
  5. [Employee Grade](https://docs.frappe.io/hr/employee-grade)
  6. [Employee Group](https://docs.frappe.io/hr/employee-group)



##### Branch

administrator edited 9 months ago

×

**A Branch office is an outlet of a company located at a different location, other than the main office.**

Frappe HR allows you to create and keep a record of the different branches of your organization.

To access Branch, go to:

> **Home > Human Resources > Employee > Branch**

## **1\. Prerequisites**

Before creating a Branch, it is mandatory you create the following documents:

  * [Company](https://frappehr.com/docs/v14/user/manual/en/setting-up/company-setup)




## **2\. How to create a Branch**

  1. Go to the Branch list, click on New.

  2. Enter the name of the Branch.

  3. Save.




![Branch](https://frappehr.com/files/branch.png)  


You can link the Branch to the [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee) master.

## **3\. Related Topics**

  1. [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee)

  2. [Employment Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/employment-type)

  3. [Department](https://frappehr.com/docs/v14/user/manual/en/human-resources/department)

  4. [Designation](https://frappehr.com/docs/v14/user/manual/en/human-resources/designation)

  5. [Employee Grade](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee-grade)

  6. [Employee Group](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee-group)




Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/department

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Department

**A Department is a specialized functional area or a division within an organization.**

You can configure the Departments in your organization, set Leave Block List, and also Leave and Expense Approvers for the same.

To access Department, go to:

> Home > Human Resources > Employee > Department

Department is a tree-structured master, which means you can create parent departments and sub-departments as shown below:

![Department Tree](https://frappehr.com/files/department-tree.png)

> **Note:** The 'Is Group' checkbox needs to be checked if the Department is a parent department.

## **1\. Prerequisites**

Before creating a Department, it is advisable you create the following documents:

  * [Company](https://docs.erpnext.com/docs/user/manual/en/company-setup)



## **2\. How to create a Department**

  1. Go to the Department list, click on New.
  2. Enter Department name.
  3. Select Company name.
  4. Select Leave Block List (optional) applicable for this department.
  5. Save.



![Department](https://frappehr.com/files/department.png)

## **3\. Features**

### **3.1 Leave and Expense Approvers**

You can set Leave and Expense Approvers for a particular Department in the 'Leave Approver' and 'Expense Approver' table respectively.

![Leave and Expense Approver](https://frappehr.com/files/leave-and-expense.png)

> **Note:** Multiple Leave and Expense Approvers can be set for a particular Department. However, the first Approver in the list will be set as the default Approver.

## **4\. Related Topics**

  1. [Employment Type](https://docs.frappe.io/hr/employment-type)
  2. [Employee Grade](https://docs.frappe.io/hr/employee-grade)
  3. [Employee Branch](https://docs.frappe.io/hr/branch)
  4. [Employee Designation](https://docs.frappe.io/hr/designation)



##### Department

administrator edited 9 months ago

×

**A Department is a specialized functional area or a division within an organization.**

You can configure the Departments in your organization, set Leave Block List, and also Leave and Expense Approvers for the same.

To access Department, go to:

> Home > Human Resources > Employee > Department

Department is a tree-structured master, which means you can create parent departments and sub-departments as shown below:

![Department Tree](https://frappehr.com/files/department-tree.png)  


> **Note:** The 'Is Group' checkbox needs to be checked if the Department is a parent department.

## **1\. Prerequisites**

Before creating a Department, it is advisable you create the following documents:

  * [Company](https://frappehr.com/docs/v14/user/manual/en/setting-up/company-setup)




## **2\. How to create a Department**

  1. Go to the Department list, click on New.

  2. Enter Department name.

  3. Select Company name.

  4. Select Leave Block List (optional) applicable for this department.

  5. Save.

![Department](https://frappehr.com/files/department.png)  





## **3\. Features**

### **3.1 Leave and Expense Approvers**

You can set Leave and Expense Approvers for a particular Department in the 'Leave Approver' and 'Expense Approver' table respectively.

![Leave and Expense Approver](https://frappehr.com/files/leave-and-expense.png)  


> **Note:** Multiple Leave and Expense Approvers can be set for a particular Department. However, the first Approver in the list will be set as the default Approver.

## **4\. Related Topics**

  1. [Employment Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/employment-type)

  2. [Employee Grade](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee-grade)

  3. [Employee Branch](https://frappehr.com/docs/v14/user/manual/en/human-resources/branch)

  4. [Employee Designation](https://frappehr.com/docs/v14/user/manual/en/human-resources/designation)




Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/designation

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Designation

**Designations are the official job titles given to employees.**

With reference to company management, there are various stakeholders like directors, officers, managers and shareholders who guide a company towards the fulfillment of its business objectives.

Frappe HR allows you to create various designations and also mention the skills required for the same.

To access Designation, go to:

> **Home > Human Resources > Employee > Designation**

## **1\. How to create a Designation**

  1. Go to Designation list, click on New.
  2. Enter the Designation name.
  3. Enter Description and Required Skills (optional).
  4. Save.



![Designation](https://frappehr.com/files/designation.png)

> **Note: The skills required for an Employee of a particular Designation that are specified in the "Required Skills" section of the Designation doctype can be directly fetched in the** [**Employee Skill Map**](https://docs.frappe.io/hr/employee-skill-map) **to evaluate an employee's performance based on his skills.**

## **3\. Related Topics**

  1. [Employee](https://docs.frappe.io/hr/employee)
  2. [Employment Type](https://docs.frappe.io/hr/employment-type)
  3. [Branch](https://docs.frappe.io/hr/branch)
  4. [Department](https://docs.frappe.io/hr/department)
  5. [Employee Grade](https://docs.frappe.io/hr/employee-grade)
  6. [Employee Skill Map](https://docs.frappe.io/hr/employee-skill-map)



##### Designation

administrator edited 9 months ago

×

**Designations are the official job titles given to employees.**

With reference to company management, there are various stakeholders like directors, officers, managers and shareholders who guide a company towards the fulfillment of its business objectives.

Frappe HR allows you to create various designations and also mention the skills required for the same.

To access Designation, go to:

> **Home > Human Resources > Employee > Designation**

## **1\. How to create a Designation**

  1. Go to Designation list, click on New.

  2. Enter the Designation name.

  3. Enter Description and Required Skills (optional).

  4. Save.

![Designation](https://frappehr.com/files/designation.png)  





> **Note: The skills required for an Employee of a particular Designation that are specified in the "Required Skills" section of the Designation doctype can be directly fetched in the**[**Employee Skill Map**](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee_skill_map)**to evaluate an employee's performance based on his skills.**

## **3\. Related Topics**

  1. [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee)

  2. [Employment Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/employment-type)

  3. [Branch](https://frappehr.com/docs/v14/user/manual/en/human-resources/branch)

  4. [Department](https://frappehr.com/docs/v14/user/manual/en/human-resources/department)

  5. [Employee Grade](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee-grade)

  6. [Employee Skill Map](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee_skill_map)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-grade

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Grade

**An Employee Grade is a grouping of employees with similar positions or values in order to assign compensation rates and salary structures.**

Frappe HR allows you to configure Employee Grades, helping you to easily categorize employees based on seniority or any other criteria.

Employee Grade also helps you fetch Employee records in bulk based on their grades while processing payroll, allocating leaves, etc.

To access Employee Grade, go to:

> **Home > Human Resources > Employee > Employee Grade**

## **1\. Prerequisites**

Before creating an Employee Grade, it is advisable you create the following documents:

  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  * [Salary Structure](https://docs.frappe.io/hr/salary-structure)



## **2\. How to create an Employee Grade**

  1. Go to Employee Grade list, click on New.
  2. Enter the Name of the Employee Grade.
  3. Enter the Default [Leave Policy](https://docs.frappe.io/hr/leave-policy) and Default [Salary Structure](https://docs.frappe.io/hr/salary-structure) for that Grade.
  4. Save.



![Employee Grade](https://frappehr.com/files/employee-grade.png)

Shown below are the different Employee Grades created.

![New Employee Grade](https://frappehr.com/files/employee-grade1.png)

You also can access [Employee](https://docs.frappe.io/hr/employee), [Leave Period](https://docs.frappe.io/hr/leave-type), [Employee Onboarding Template](https://docs.frappe.io/hr/employee-onboarding) and [Employee Separation Template](https://docs.frappe.io/hr/employee-separation).

## **3\. Related Topics**

  1. [Leave Type](https://docs.frappe.io/hr/leave-type)



last

##### Employee Grade

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-group

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Group

**Employee Group is grouping of Employees based on some attributes such as Designation, Grade, Branch, etc.**

To access Employee Group, go to:

> **Home > Human Resources > Employee > Employee Group**

## **1\. Prerequisites**

Before creating an Employee Group, it is advisable you create the following documents:

  * [Employee](https://docs.frappe.io/hr/employee)



## **2\. How to create an Employee Group**

  1. Go to Employee Group list, click on New.
  2. Enter the Name.
  3. Select and add Employee ID to the group. The Employee Name will get automatically fetched.
  4. Save.



![Employee Group](https://frappehr.com/files/employee-group.png)

## **3\. Features**

### **3.1 Service Level Agreement**

An Employee Group can be added to the [Service Level Agreement](https://docs.erpnext.com/docs/v14/user/manual/en/support/service-level-agreement) doctype, where the Service Level can be specified for a particular Employee Group.

## **4\. Related Topics**

  1. [Employment Type](https://docs.frappe.io/hr/employment-type)
  2. [Branch](https://docs.frappe.io/hr/branch)
  3. [Department](https://docs.frappe.io/hr/department)
  4. [Designation](https://docs.frappe.io/hr/designation)
  5. [Employee Grade](https://docs.frappe.io/hr/employee-grade)



last

##### Employee Group

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-health-insurance

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Health Insurance

**Employee Health Insurance is a benefit extended by the company to their employees. When a company provides health insurance assistance, they pay full or part premiums for the health insurance policy.**

Frappe HR allows you to save the Employee Health Insurance detail and link it to that particular Employee's master.

To access Health Insurance Provider document, go to:

> **Home > Human Resources > Employee > Employee Health Insurance**

## **1\. How to create an Employee Health Insurance**

  1. Go to Employee Health Insurance list, click on New.
  2. Enter the Health Insurance Name.
  3. Save.



![Employee](https://frappehr.com/files/health-insurance.png)

Additionally, in the Employee master, you can attach the Health Insurance Provider Name and fill in the Health Insurance No.

![Employee](https://frappehr.com/files/employee-health-insurance.png)

## **2\. Related Topics**

  1. [Employee](https://docs.frappe.io/hr/employee)



last

##### Employee Health Insurance

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/organizational-chart

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Organizational Chart

**An organizational chart shows the organizational structure by depicting connections between different employees with their designation, image, and name. In large organizations where it's difficult to remember names, people can interact with the org chart and know the hierarchy through faces and other info.**

To access Employee Grade, go to:

> Home > Human Resources > Employee > Organizational Chart

  * The org chart is created based on the "Reports To" field in Employee master.
  * The employees that don't report to anyone are shown in the first level.
  * The chart expands horizontally on the desktop view and vertically on mobile.
  * Each node has details like the Employee Name, Image, Designation, and the total number of connections.
  * The connections are the total number of descendants a particular node has till the end of the hierarchy.
  * The edit button in every node navigates to the Employee Master.
  * As you navigate through the chart, the active hierarchy is highlighted.
  * You can use the Company filter to check the org chart for individual companies.



![Org Chart](https://frappehr.com/files/org-chart.png)

![Org Chart Mobile](https://frappehr.com/files/org-chart-mobile-2.png)

##### Organizational Chart

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/attendance

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Attendance

**Attendance is a record stating whether an Employee has been present on a particular day or not.**

In Frappe HR, you can mark and record attendance of an Employee on a daily basis using the Attendance doctype.

To access Attendance, go to:

> Home > Human Resources > Attendance

## **1\. Prerequisites**

Before creating an Attendance record, it is advised that you create the following first:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Shift Type](https://docs.frappe.io/hr/shift-management)



## **2\. How to create an Attendance**

  1. Go to the Attendance list, click on New.
  2. Select the Employee.
  3. Select the Attendance Date.
  4. Select the Shift (optional).
  5. Select the Status (Present, Absent, On Leave, Half Day).
  6. Save and Submit.



![Attendance](https://frappehr.com/files/attendance.png)

> **Note:** Attendance cannot be marked for future dates.

You can get a monthly report of your Attendance data by going to the **Monthly Attendance Details** report.

You can easily set attendance for Employees using the [Employee Attendance Tool](https://docs.frappe.io/hr/employee-attendance-tool).

You can also bulk upload attendance using the [Upload Attendance](https://docs.frappe.io/hr/upload-attendance).

## **3\. Features**

### **3.1 Marking Unmarked Attendance**

In case the attendance for some employees is not marked, you can mark them as present, absent, or half-day.

#### **How to Mark Attendance**

  1. Go to the Attendance list.
  2. Click on the **Mark Attendance** button.
  3. A dialog will appear.
  4. Select the Employee and Month.
  5. Select the Status whether Present, Absent, or Half Day.
  6. If you want to exclude holidays while doing so, check _Exclude Holidays_.
  7. Select the dates on which you want to mark attendance for a selected Employee.
  8. Click on the **Mark Attendance** button and click on **Yes**.



![Attendance](https://frappehr.com/files/mark-attendance.gif)

## **4\. Related Topics**

  1. [Employee Attendance Tool](https://docs.frappe.io/hr/employee-attendance-tool)
  2. [Shift Management](https://docs.frappe.io/hr/shift-management)
  3. [Auto Attendance](https://docs.frappe.io/hr/auto-attendance)
  4. [Upload Attendance](https://docs.frappe.io/hr/upload-attendance)
  5. [Attendance Request](https://docs.frappe.io/hr/attendance-request)



It is also, possible to set up marking of attendance automatically based on check-in/check-out logs from Biometric/RFID Devices (or any other similar mechanisms that produce IN/OUT logs of the employee). Please refer to [Auto Attendance](https://docs.frappe.io/hr/auto-attendance) feature for more information.

##### Attendance

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-attendance-tool

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Attendance Tool

**Employee Attendance Tool allows you to mark the attendance of multiple employees for a particular date.**

To access the Employee Attendance Tool, go to:

> **Home > Human Resources > Attendance > Employee Attendance Tool**

This tool allows you to add attendance records for multiple employees based on their Department and Branch for a given day quickly.

## **1\. Prerequisites**

Before creating an Employee Attendance, it is advised that you create the following first:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Department](https://docs.frappe.io/hr/department)
  * [Branch](https://docs.frappe.io/hr/branch)



## **2\. How to mark attendance using Employee Attendance Tool**

  1. Enter the Date.
  2. Select the Department and Branch (optional).
  3. Select the Employees.
  4. Mark Present, Absent, or Half Day as required.
  5. You can also select the shift and mark employees with Late Entry and Early Exit from this view.
  6. The Marked Attendance table below the tool will show you all the employees you have marked attendance for along with their attendance status. You can use column filters to search for a particular employee or employees with a particular status form this table.



![](https://frappehr.com/files/pOo6Zsf.gif)

> **Note: Attendance cannot be marked for future dates.**

Once the attendance is marked using the Employee Attendance Tool, the employee attendance is saved in the [Attendance](https://docs.frappe.io/hr/attendance) record as shown:

![](https://frappehr.com/files/dxhxNaV.png)

## **3\. Related Topics**

  1. [Attendance](https://docs.frappe.io/hr/attendance)
  2. [Attendance Request](https://docs.frappe.io/hr/attendance-request)
  3. [Upload Attendance](https://docs.frappe.io/hr/upload-attendance)
  4. [Shift Management](https://docs.frappe.io/hr/shift-management)
  5. [Auto Attendance](https://docs.frappe.io/hr/auto-attendance)



##### Employee Attendance Tool

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/attendance-request

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Attendance Request

**Using the Attendance Request, employees can submit their attendance request for the days when their attendance wasn't marked due to various reasons such as on-site duty or work from home. Attendance Request can also be used for Attendance Regularization to overwrite existing attendance records.**

To access Attendance Request, go to:

> Home > Human Resources > Attendance > Attendance Request

## **1\. Prerequisites**

Before creating an Attendance Request, it is advised that you create the following first:

  * [Employee](https://docs.frappe.io/hr/employee)



## **2\. How to create an Attendance Request**

  1. Go to Attendance Request list, click on New.
  2. Select Employee who wants to submit the Attendance Request.
  3. Select From Date and To Date of Attendance Request.
  4. You can enable **"Include Holidays"** if you want to mark attendance for holidays in between these dates too. This feature was introduced v15 onwards.
  5. Select Reason and enter Explanation (optional).
  6. Save and Submit.



![Attendance Request](https://frappehr.com/files/attendance-request.png)

> **Note 1:** You can check the 'Half Day' checkbox and enter the Date in case the attendance is for Half Day.

> **Note 2:** On submission of the same, Attendance documents will be created for the days you mentioned as shown.

![Attendance Request Submit](https://frappehr.com/files/attendance-request-submission.png)

As seen below, respective Attendance records are linked with the submitted Attendance Request.

![Attendance Request Linked](https://frappehr.com/files/attendance-request-link.png)

If you cancel the Attendance Request, the linked Attendance documents created will be cancelled as well.

![Attendance Request Cancelled](https://frappehr.com/files/attendance-request-cancelled.png)

## **3\. Features**

### **3.1 Overwrite an existing Attendance record**

Consider a scenario where the auto-attendance tool marked an employee as Absent. If the employee wants to rectify their attendance, they can raise a request. On submission, the Attendance record will be updated:

![changed status](https://frappehr.com/files/changed%20status.png)

The submission can be controlled via [workflows](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/workflows) to undergo approvals.

### **3.2 Request for Attendance in Bulk**

Employees can also request for attendance for an entire month or week. On submission, attendance marking is skipped for holidays or leave days.

You can enable "Include Holidays" if you want to mark attendance for holidays too.

![](https://frappehr.com/files/FYdSpw9.png)

Attendance warnings are shown on the request dashboard for the same:

![attendance warnings](https://frappehr.com/files/attendance-warnings.png)

## **4\. Related Topics**

  1. [Employee Attendance Tool](https://docs.frappe.io/hr/employee-attendance-tool)
  2. [Shift Management](https://docs.frappe.io/hr/shift-management)
  3. [Auto Attendance](https://docs.frappe.io/hr/auto-attendance)
  4. [Upload Attendance](https://docs.frappe.io/hr/upload-attendance)
  5. [Attendance](https://docs.frappe.io/hr/attendance)



##### Attendance Request

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/upload-attendance

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Upload Attendance

**This tool helps you to upload bulk attendance from a csv file.**

To upload the attendance go to:

> Home > Human Resources > Attendance > Upload Attendance

## 1\. Prerequisites

Before uploading bulk Attendance record, it is advised that you create the following first:

  * [Employee](/erpnext/v13/user/manual/en/human-resources/employee)



## 2\. How to upload attendance in bulk

  1. Enter Attendance From Date and Attendance To Date.
  2. Click on 'Get Template' button. It will download a csv file with the Employee Details as shown.

![Attendance Template](/files/upload-attendance1.png)

  3. Enter the Status (Present / Absent) of the Employee for the selected dates.

  4. Save the file.
  5. Upload the saved file.

![Attendance upload](/files/upload-attendance.png)




Once the bulk attendance is uploaded, respective attendance records will be created.

## 3\. Related Topics

  1. [Employee Attendance Tool](/erpnext/v13/user/manual/en/human-resources/employee-attendance-tool)
  2. [Shift Management](/erpnext/v13/user/manual/en/human-resources/shift-management)
  3. [Auto Attendance](/erpnext/v13/user/manual/en/human-resources/auto-attendance)
  4. [Attendance Request](/erpnext/v13/user/manual/en/human-resources/attendance-request)
  5. [Attendance](/erpnext/v13/user/manual/en/human-resources/attendance)



##### Upload Attendance

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-checkin

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Checkin

Employee Checkin is used to keep a log of all the check-ins and check-outs of an employee in the organization. Most organizations use this for attendance, shift management, and working hours calculations.

## **1\. Prerequisites**

To create an Employee Checkin, you need to first create:

  * [Employee](https://docs.frappe.io/hr/employee)



If you want shifts to be determined in employee checkins and want to process auto-attendance, then you need to create the following documents too:

  * [Shift Type](https://docs.frappe.io/hr/shift-type)
  * [Shift Assignment](https://docs.frappe.io/hr/shift-assignment) or set a default shift in Employee master.



## **2\. How to create an Employee Checkin**

#### **2.1 Creating logs manually**

To create a new Employee Checkin go to:

> **Human Resources > Attendance > Employee Checkin**

  1. Click on New.
  2. Select the Employee.
  3. Set the date and time for the log.
  4. Set Log Type as IN/OUT.
  5. Save.
  6. If you have set up shifts and shift assignments, the Employee Checkin will set the appropriate shift in which the timestamp falls after saving. If you have forgotten to assign a shift, causing the system to pick up the wrong one, you can re-fetch it by clicking on the 'Fetch Shift' button, as long as attendance has not already been marked for the same.
  7. You can enable _Skip Auto Attendance_ to skip that record while marking attendance.
  8. You can also capture the location from where the employee has checked in or the Biometric Device ID.



![](/files/qkGD7oD.png)

If auto attendance is enabled, the attendance record marked for a set of check-ins will be linked to the document later.

#### **2.2 Integrating Frappe HR with Biometric Devices**

If you are using a Biometric Device to log employee check-ins and check-outs you can use it to create records in Frappe HR. You can read more about this [here](https://docs.frappe.io/hr/integrating-frappe-hr-with-biometric-attendance-devices).

## **3\. Features**

### **3.1 Geolocation Tracking**

You can also track geolocation in employee checkins. To enable this, go to HR Settings and enable "**Allow Geolocation Tracking** "

![](https://frappehr.com/files/UqR7S9c.png)

You can then click on the "**Fetch Geolocation** " button in the check-in form to fetch your current location

![geolocation-desk](https://frappehr.com/files/geolocation-desk.gif)

It geolocation tracking has been enabled, it will be automatically captured while checking in from the mobile app too

![geolocation-mobile](https://frappehr.com/files/geolocation-mobile.gif)

### **3.2 Checkin log indicator**

Employee checkins fetch appropriate shift while saving based on the time of checkin or checkout log. If there is no active shift asssociated for the time of log, the employee checkin log is marked as **Off-Shift** indicating the lack of associated shift. Since auto-attendance works based on shift, these checkin logs are excluded while marking auto-attendance.

![](/private/files/Screenshot%202025-07-11%20at%2011-06-45%20Jane%20Doe%20-%20EMP-CKIN-07-2025-000001.png)

##### Employee Checkin

asmita edited 1 month ago

×

Employee Checkin is used to keep a log of all the check-ins and check-outs of an employee in the organization. Most organizations use this for attendance, shift management, and working hours calculations.

## **1\. Prerequisites**

To create an Employee Checkin, you need to first create:

  * [Employee](https://docs.frappe.io/hr/employee)



If you want shifts to be determined in employee checkins and want to process auto-attendance, then you need to create the following documents too:

  * [Shift Type](https://docs.frappe.io/hr/shift-type)
  * [Shift Assignment](https://docs.frappe.io/hr/shift-assignment) or set a default shift in Employee master.



## **2\. How to create an Employee Checkin**

#### **2.1 Creating logs manually**

To create a new Employee Checkin go to:

> **Human Resources > Attendance > Employee Checkin**

  1. Click on New.
  2. Select the Employee.
  3. Set the date and time for the log.
  4. Set Log Type as IN/OUT.
  5. Save.
  6. If you have set up shifts and shift assignments, the Employee Checkin will set the appropriate shift in which the timestamp falls after saving. If you have forgotten to assign a shift, causing the system to pick up the wrong one, you can re-fetch it by clicking on the 'Fetch Shift' button, as long as attendance has not already been marked for the same.
  7. You can enable _Skip Auto Attendance_ to skip that record while marking attendance.
  8. You can also capture the location from where the employee has checked in or the Biometric Device ID.



![](/files/qkGD7oD.png)

If auto attendance is enabled, the attendance record marked for a set of check-ins will be linked to the document later.

#### **2.2 Integrating Frappe HR with Biometric Devices**

If you are using a Biometric Device to log employee check-ins and check-outs you can use it to create records in Frappe HR. You can read more about this [here](https://docs.frappe.io/hr/integrating-frappe-hr-with-biometric-attendance-devices).

## **3\. Features**

### **3.1 Geolocation Tracking**

You can also track geolocation in employee checkins. To enable this, go to HR Settings and enable "**Allow Geolocation Tracking** "

![](https://frappehr.com/files/UqR7S9c.png)

You can then click on the "**Fetch Geolocation** " button in the check-in form to fetch your current location

![geolocation-desk](https://frappehr.com/files/geolocation-desk.gif)

It geolocation tracking has been enabled, it will be automatically captured while checking in from the mobile app too

![geolocation-mobile](https://frappehr.com/files/geolocation-mobile.gif)

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/auto-attendance

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Auto Attendance

Auto attendance marks the attendance for employees assigned to a shift based on the records in the [Employee Checkin](https://docs.frappe.io/hr/employee-checkin) document and the [Auto Attendance Settings](https://docs.frappe.io/hr/shift-type#2-auto-attendance-settings) of that shift.

> **Note:** [**Shift Type**](/hr/shift-type) **needs to be set up and assigned to employees before creating 'Employee Checkin' records. Attendance will be marked by Auto Attendance only for check-in records that are created after setting up and assigning an employee to their shift type.**

## **Steps to Set Up Auto Attendance**

You can set up Auto Attendance by following the steps mentioned below:

### **1\. Define Shift Type with Auto Attendance Enabled**

You will have to define a Shift Type with Auto Attendance enabled. Details can be found [here](https://docs.frappe.io/hr/shift-type).

### **2\. Assign these shifts to employees**

Once you have set up a shift, you will have to assign this shift to the employees. You can assign this to an employee using one of the two methods given below:

  * **Using the Shift Assignment** : You can use the [Shift Assignment](https://docs.frappe.io/hr/shift-assignment) document to assign shifts to employees on a date to date basis.
  * **Using the Default Shift field in the employee master** : Sometimes you would want to assign a shift for an employee for all the days. You can do this by setting the following field in the Employee: `> Employee > Attendance and Leave Details > Default Shift`



> **Note: Setting Shift Assignment takes precedence over the Default Shift. i.e. if you have set up a shift assignment as well as a default shift for an employee, the system will consider the assigned shift over a default shift.**

### **3\. Setup Attendance Device ID field in Employee**

Biometric systems usually have their own IDs for employees. But, the Employee Checkin in Frappe HR needs to be mapped to an employee.

To map the employee to their IDs in the Biometric system you need to set the following field with the appropriate value: `Employee > Attendance and Leave Details > Attendance Device ID (Biometric/RF tag ID)`

### **4\. Import or sync Employee Checkins**

Once you are done with the above steps you can import/sync the [Employee Checkin](https://docs.frappe.io/hr/employee-checkin) and start generating attendance automatically.

Please refer to this article to know more about populating Employee Checkins from an external system: [Integrating Frappe HR With Biometric Attendance Devices](https://docs.frappe.io/hr/integrating-frappe-hr-with-biometric-attendance-devices)

## **Frequently Asked Questions**

### **1\. How are a shift's actual start and end timings determined?**

Consider a Morning Shift:

  * Start Time: 08:00:00
  * End Time: 11:30:00
  * Begin check-in before shift start time (in minutes): 60
  * Allow check-out after shift end time (in minutes): 60



So the "Actual Start Time" of the shift = _Start Time - Begin check-in before shift start time_ = 07:00:00

The "Actual End Time" of the shift = _End Time + Allow check-out after shift end time_ = 12:30:00.

### **2\. When is the attendance marked automatically for a particular shift?**

Auto Attendance for every 'Shift Type' record is attempted to be marked every hour. You can also trigger the auto attendance manually for a single shift type by pressing the 'Mark Auto Attendance' button in the Shift Type document.

Once the "**Last Sync of Checkin** " passes the shift's actual end time, all the employee checkins for that shift are processed for marking attendance.

For eg: Consider a Morning Shift:

  * Start Time: 08:00:00
  * End Time: 11:30:00
  * Begin check-in before shift start time (in minutes): 60
  * Allow check-out after shift end time (in minutes): 60



So the "Actual Start Time" of the shift is 07:00:00 and the actual end time of the shift is 12:30:00.

Once the "Last Sync of Checkin" timestamp passes 12:30:00, it indicates that all possible checkin records for that particular shift have been synced/captured and this is when attendance marking is attempted.

### **3\. How does Auto Attendance determine shift for an Employee?**

The shift of an Employee on a particular date is determined by the following steps:

  * Shift assigned to an Employee on the given date in the 'Shift Assignment' document.
  * If the above is not found, the shift is picked up from the 'Default Shift' field of the 'Employee' document.
  * Finally, if a shift is not found in 'Employee' document also, then it is assumed that the Employee does not belong to any shift on the given date and no attendance is attempted to be marked by the Auto Attendance job.



### **4\. How does Auto Attendance determine Holiday List for an Employee?**

Holiday List for an employee is determined as follows:

  * If the employee's determined 'Shift Type' has a holiday list, then this is considered.
  * Otherwise, the holiday list is fetched from either the 'Holiday List' field in the Employee document or from the 'Default Holiday List' field in the Company document, in that order.



Note: The Holiday List is important to be determined correctly by the Auto Attendance to not mark the employee as 'Absent' on holidays.

### **5\. Most Biometric devices don't return the exact Log Type. In such cases how will the auto attendance determine which log is IN/OUT and how does it calculate working hours?**

This is determined by 2 fields in the Shift Type set up:

  * Determine Check-in and Check-out
  * Working Hours Calculation Based On



It has been explained in detail over [here](https://docs.frappe.io/hr/shift-type#2-auto-attendance-settings).

##### Auto Attendance

administrator edited 9 months ago

×

Auto attendance marks the attendance for employees assigned to a shift based on the records in the [Employee Checkin](https://docs.frappe.io/hr/employee-checkin) document and the [Auto Attendance Settings](https://docs.frappe.io/hr/shift-type#2-auto-attendance-settings) of that shift.

> **Note:** [**Shift Type**](/hr/shift-type) **needs to be set up and assigned to employees before creating 'Employee Checkin' records. Attendance will be marked by Auto Attendance only for check-in records that are created after setting up and assigning an employee to their shift type.**

## **Steps to Set Up Auto Attendance**

You can set up Auto Attendance by following the steps mentioned below:

### **1\. Define Shift Type with Auto Attendance Enabled**

You will have to define a Shift Type with Auto Attendance enabled. Details can be found [here](https://docs.frappe.io/hr/shift-type).

### **2\. Assign these shifts to employees**

Once you have set up a shift, you will have to assign this shift to the employees. You can assign this to an employee using one of the two methods given below:

  * **Using the Shift Assignment** : You can use the [Shift Assignment](https://docs.frappe.io/hr/shift-assignment) document to assign shifts to employees on a date to date basis.
  * **Using the Default Shift field in the employee master** : Sometimes you would want to assign a shift for an employee for all the days. You can do this by setting the following field in the Employee: `> Employee > Attendance and Leave Details > Default Shift`



> **Note: Setting Shift Assignment takes precedence over the Default Shift. i.e. if you have set up a shift assignment as well as a default shift for an employee, the system will consider the assigned shift over a default shift.**

### **3\. Setup Attendance Device ID field in Employee**

Biometric systems usually have their own IDs for employees. But, the Employee Checkin in Frappe HR needs to be mapped to an employee.

To map the employee to their IDs in the Biometric system you need to set the following field with the appropriate value: `Employee > Attendance and Leave Details > Attendance Device ID (Biometric/RF tag ID)`

### **4\. Import or sync Employee Checkins**

Once you are done with the above steps you can import/sync the [Employee Checkin](https://docs.frappe.io/hr/employee-checkin) and start generating attendance automatically.

Please refer to this article to know more about populating Employee Checkins from an external system: [Integrating Frappe HR With Biometric Attendance Devices](https://docs.frappe.io/hr/integrating-frappehr-with-biometric-attendance-devices)

## **Frequently Asked Questions**

### **1\. How are a shift's actual start and end timings determined?**

Consider a Morning Shift:

  * Start Time: 08:00:00
  * End Time: 11:30:00
  * Begin check-in before shift start time (in minutes): 60
  * Allow check-out after shift end time (in minutes): 60



So the "Actual Start Time" of the shift = _Start Time - Begin check-in before shift start time_ = 07:00:00

The "Actual End Time" of the shift = _End Time + Allow check-out after shift end time_ = 12:30:00.

### **2\. When is the attendance marked automatically for a particular shift?**

Auto Attendance for every 'Shift Type' record is attempted to be marked every hour. You can also trigger the auto attendance manually for a single shift type by pressing the 'Mark Auto Attendance' button in the Shift Type document.

Once the "**Last Sync of Checkin** " passes the shift's actual end time, all the employee checkins for that shift are processed for marking attendance.

For eg: Consider a Morning Shift:

  * Start Time: 08:00:00
  * End Time: 11:30:00
  * Begin check-in before shift start time (in minutes): 60
  * Allow check-out after shift end time (in minutes): 60



So the "Actual Start Time" of the shift is 07:00:00 and the actual end time of the shift is 12:30:00.

Once the "Last Sync of Checkin" timestamp passes 12:30:00, it indicates that all possible checkin records for that particular shift have been synced/captured and this is when attendance marking is attempted.

### **3\. How does Auto Attendance determine shift for an Employee?**

The shift of an Employee on a particular date is determined by the following steps:

  * Shift assigned to an Employee on the given date in the 'Shift Assignment' document.
  * If the above is not found, the shift is picked up from the 'Default Shift' field of the 'Employee' document.
  * Finally, if a shift is not found in 'Employee' document also, then it is assumed that the Employee does not belong to any shift on the given date and no attendance is attempted to be marked by the Auto Attendance job.



### **4\. How does Auto Attendance determine Holiday List for an Employee?**

Holiday List for an employee is determined as follows:

  * If the employee's determined 'Shift Type' has a holiday list, then this is considered.
  * Otherwise, the holiday list is fetched from either the 'Holiday List' field in the Employee document or from the 'Default Holiday List' field in the Company document, in that order.



Note: The Holiday List is important to be determined correctly by the Auto Attendance to not mark the employee as 'Absent' on holidays.

### **5\. Most Biometric devices don't return the exact Log Type. In such cases how will the auto attendance determine which log is IN/OUT and how does it calculate working hours?**

This is determined by 2 fields in the Shift Type set up:

  * Determine Check-in and Check-out
  * Working Hours Calculation Based On



It has been explained in detail over [here](https://docs.frappe.io/hr/shift-type#2-auto-attendance-settings).

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leaves

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leaves

This section will help you understand how Frappe HR enables you to efficiently manage the leave schedule of your organization. It also explains how employees can apply for leaves.

The number and type of leaves an Employee can apply is controlled by Leave Allocation. You can create Leave Allocation for a Leave Period based on the Company's Leave Policy. You can also allocate Additional Leaves to your employees and generate reports to track leaves taken by Employees.

Employees can also create leave requests, which their respective managers (leave approvers) can approve or reject. An Employee can select leaves from a number of leave types such as Sick Leave, Casual Leave, Privilege Leave and so on.

## **Related Topics**

  1. [Holiday List](https://docs.frappe.io/hr/holiday-list)
  2. [Leave Type](https://docs.frappe.io/hr/leave-type)
  3. [Leave Period](https://docs.frappe.io/hr/leave-period)
  4. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  5. [Leave Policy Assignment](https://docs.frappe.io/hr/leave-policy-assignment)
  6. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  7. [Leave Control Panel](https://docs.frappe.io/hr/leave-control-panel)
  8. [Leave Application](https://docs.frappe.io/hr/leave-application)
  9. [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request)
  10. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)
  11. [Leave Block List](https://docs.frappe.io/hr/leave-block-list)
  12. [Leave Ledger Entry](https://docs.frappe.io/hr/leave-ledger-entry)



##### Leaves

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/holiday-list

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Holiday List

**Holiday List is a list which contains the dates of holidays.**

Most organizations have a standard Holiday List for their employees. However, some of them may have different holiday lists based on different Locations or Departments. In ERPNext, you can configure multiple Holiday Lists and assign them to your employees based on your requirements.

To access Holiday List, go to:

> **Home > Human Resources > Leaves > Holiday List**

## **1\. How to create a Holiday List**

  1. Go to Holiday List, click on New.
  2. Enter Holiday List Name. It can be based on the Fiscal Year or Location or Department as per the requirement.
  3. Select From Date and To Date for the Holiday List.



![Holiday List](https://frappehr.com/files/holiday-list-1.png)

## **2\. Features**

Some of the additional features in the Holiday List are as follows:

### **2.1 Adding Weekly Holidays**

You can quickly add Weekly Offs in the Holiday List as follows:

  1. In the 'Add Weekly Holidays' section, select the day in the Weekly Off field.
  2. Click on the 'Add to Holidays' button.



![Holiday List](https://frappehr.com/files/holiday-list-2.gif)

### **2.2 Adding Local Holidays**

You can quickly add local holidays to the Holiday List as follows:

  1. In the 'Add Local Holidays' section, select the country.
  2. Some countries have subdivisions with different or additional holidays. If you like, you can optionally select a specific subdivision.
  3. Click on the 'Add to Holidays' button.



#### Can't find your country for fetching local holidays in Holiday List?

Frappe HR uses an external package Holidays to get predictable holiday dates for countries. These are the list of countries supported:

<https://holidays.readthedocs.io/en/latest/#available-countries>

If your country is not listed here, you will have to manually add local holidays. If you are a developer, you can also consider contributing your country’s holidays to the [original repository](https://github.com/vacanza/holidays).

### **2.3 Adding Holidays manually**

You can also add specific days manually by clicking on the 'Add row' option in the Holidays table.

![Holiday List](https://frappehr.com/files/holiday-list-3.png)

## **3\. Holiday List in Company**

You can set a default Holiday List at the company-level in the Company master in the 'Default Holiday List' field.

![Holiday List](https://frappehr.com/files/default-holiday-list-company.png)

## **4\. Holiday List in Employee**

If you have created multiple Holiday List, select a specific Holiday List for an Employee in the respective master.

When an Employee applies for Leave, the days mentioned in the Holiday List will not be counted, as they are holidays already.

> **Note: If you have specified a Holiday List in the Employee master, then that Holiday List will be given priority as compared to the default Holiday List of the Company. You can form as many holiday lists as you wish. For example, if you have a factory, you can have one list for the factory workers and another list for office staff. You can manage between many lists by linking a Holiday List to the respective Employee.**

## **5\. Holiday List in Workstation**

You can also set a Holiday List at workstation-level as shown in the screenshot below.

![Holiday List](https://frappehr.com/files/holiday-list-workstation.png)

The dates in the Holiday List tagged in the [Workstation](https://docs.erpnext.com/docs/user/manual/en/workstation) master will be considered as the days the Workstation will remain closed.

## **6\. Related Topics**

  1. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  2. [Leave Period](https://docs.frappe.io/hr/leave-period)
  3. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  4. [HR Settings](https://docs.frappe.io/hr/hr-settings)



##### Holiday List

rucha edited 8 months ago

×

**Holiday List is a list which contains the dates of holidays.**

Most organizations have a standard Holiday List for their employees. However, some of them may have different holiday lists based on different Locations or Departments. In ERPNext, you can configure multiple Holiday Lists and assign them to your employees based on your requirements.

To access Holiday List, go to:

> **Home > Human Resources > Leaves > Holiday List**

## **1\. How to create a Holiday List**

  1. Go to Holiday List, click on New.
  2. Enter Holiday List Name. It can be based on the Fiscal Year or Location or Department as per the requirement.
  3. Select From Date and To Date for the Holiday List.



![Holiday List](https://frappehr.com/files/holiday-list-1.png)

## **2\. Features**

Some of the additional features in the Holiday List are as follows:

### **2.1 Adding Weekly Holidays**

You can quickly add Weekly Offs in the Holiday List as follows:

  1. In the 'Add Weekly Holidays' section, select the day in the Weekly Off field.
  2. Click on the 'Add to Holidays' button.



![Holiday List](https://frappehr.com/files/holiday-list-2.gif)

### **2.2 Adding Local Holidays**

You can quickly add local holidays to the Holiday List as follows:

  1. In the 'Add Local Holidays' section, select the country.
  2. Some countries have subdivisions with different or additional holidays. If you like, you can optionally select a specific subdivision.
  3. Click on the 'Add to Holidays' button.



#### Can't find your country for fetching local holidays in Holiday List?

Frappe HR uses an external package Holidays to get predictable holiday dates for countries. These are the list of countries supported:

https://holidays.readthedocs.io/en/latest/#available-countries 

If your country is not listed here, you will have to manually add local holidays. If you are a developer, you can also consider contributing your country’s holidays to the original repository.

### **2.3 Adding Holidays manually**

You can also add specific days manually by clicking on the 'Add row' option in the Holidays table.

![Holiday List](https://frappehr.com/files/holiday-list-3.png)

## **3\. Holiday List in Company**

You can set a default Holiday List at the company-level in the Company master in the 'Default Holiday List' field.

![Holiday List](https://frappehr.com/files/default-holiday-list-company.png)

## **4\. Holiday List in Employee**

If you have created multiple Holiday List, select a specific Holiday List for an Employee in the respective master.

When an Employee applies for Leave, the days mentioned in the Holiday List will not be counted, as they are holidays already.

> **Note: If you have specified a Holiday List in the Employee master, then that Holiday List will be given priority as compared to the default Holiday List of the Company. You can form as many holiday lists as you wish. For example, if you have a factory, you can have one list for the factory workers and another list for office staff. You can manage between many lists by linking a Holiday List to the respective Employee.**

## **5\. Holiday List in Workstation**

You can also set a Holiday List at workstation-level as shown in the screenshot below.

![Holiday List](https://frappehr.com/files/holiday-list-workstation.png)

The dates in the Holiday List tagged in the [Workstation](https://docs.erpnext.com/docs/user/manual/en/workstation) master will be considered as the days the Workstation will remain closed.

## **6\. Related Topics**

  1. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  2. [Leave Period](https://docs.frappe.io/hr/leave-period)
  3. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  4. [HR Settings](https://docs.frappe.io/hr/hr-settings)



Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-type

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Type

**Leave Type refers to the types of leaves allocated to an Employee which they can use while making Leave Applications.**

You can create any number of Leave Types based on your company’s requirements.

To access Leave Type, go to:

> **Home > Human Resources > Leaves > Leave Type**

## **1\. How to create a Leave Type**

  1. Go to Leave Type list, click on New.
  2. Enter Leave Type Name.
  3. Enter Maximum Leave Allocation Allowed, Applicable After (Working Days), Maximum Consecutive Leaves Allowed (optional).
  4. Save.



![](/files/leave-type-settings.png)

Below is a detailed explanation of all the fields and checkboxes in Leave Type.

  * **Maximum Leave Allocation Allowed per Leave Period:** This field allows you to set the maximum number of the allocation allowed per Leave Period of this Leave Type while creating the Leave Policy.
  * **Allow Leave Application After (Working Days):** Enter the minimum number of working days here. Only the employees who have worked for this number of days or more since their Date of Joining will be allowed to apply for this particular leave type. Any other leaves (such as Casual Leave, Sick Leave.etc.) availed by the Employees after their joining date will also be considered while calculating working days of the Employee.
  * **Maximum Consecutive Leaves Allowed:** It refers to the maximum number of days this particular Leave Type can be availed at a stretch. If an employee exceeds the maximum number of days, their extended leave will be considered as ‘Leave Without Pay'.
  * **Is Carry Forward:** If checked, the balance leaves of this Leave Type will be carried forward to the next allocation period.
  * **Is Leave Without Pay:** This ensures that the Leave Type will be treated as leaves without pay and salary will get deducted for this Leave Type.
  * **Is Optional Leave:** Optional Leaves are holidays that Employees can choose to avail from a list of holidays published by the company. The Holiday List for Optional Leaves can have any number of holidays, but you can restrict the number of such leaves by setting the Max Days Leave Allowed field.
  * **Allow Negative Balance:** If checked, the system will always allow to apply and approve [Leave Applications](https://docs.frappe.io/hr/leave-application) for the Leave Type, even if there is no leave balance.
  * **Allow Over Allocation:** If checked, the system will allow allocating more leaves than the number of days in the allocation period.
  * **Include holidays within leaves as leaves:** Check this option if you wish to count holidays within leaves as a ‘leave’. For example, if an Employee has applied for leave on Friday and Monday, and Saturday and Sunday are weekly offs, if the 'Include holidays within leaves as leaves' checkbox for the Leave Type is checked, the system will consider Saturday as Sunday as leaves too. Such holidays will be deducted from the total number of leaves.
  * **Is Compensatory:** Compensatory leaves are leaves granted for working overtime or on holidays, normally compensated as an encashable leave. You can check this option to mark the Leave Type as compensatory. An Employee can request for compensatory leaves using [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request).



> **Introduced in version 13**

  * **Is Partially Paid Leaves:** This checkbox ensures that Leave Type will be treated as partially paid and some part of daily earnings will be paid through salary slip. If this checkbox is enabled then a field "Fraction of Daily Salary Per Leave" appears where you can define the fraction of daily salary paid on the partial leave day.



![](https://frappehr.com/files/8IeXPo5.png)

> **Note: The Leave Type can be either Leave Without pay or Partially Paid.**

## **2\. Features**

### **2.1 Leave Encashment**

It is possible that Employees can receive cash from their Employer for unused leaves granted to them in a Leave Period. Not all Leave Types need to be encashable, so, you should set "Allow Encashment" for only those Leave Types which are encashable.

> **Note: Leave encashment is allowed only in the last month of the Leave Period.**

![](https://frappehr.com/files/N7AiiYE.png)

**Non-Encashable Leaves:** This field indicates the number of leave days the Employees won't be able to encash. Above the mentioned days, the Employee is eligible to encash leaves.

For example, if there are 10 leaves of a particular Leave Type which is encashable, and the Employee has 8 leaves left. If Non-Encashable Leaves = 5, the Employee is given encashment of only 8 - 5 = 3 leaves.

**Earning Component:** This field allows you to specify the Salary Component that will be encashed to Employees as a part of their Salary in the Salary Slip.

> **Note: On submitting a** [**Leave Encashment**](https://docs.frappe.io/hr/leave-encashment) **for an Employee, Frappe HR automatically creates an** [**Additional Salary**](https://docs.frappe.io/hr/additional-salary) **which will get added to the Salary Slip of the Employee when processing the next payroll.**

### **2.2 Earned Leave**

Earned Leaves are leaves earned by an Employee after working with the company for a certain amount of time. Checking "Is Earned Leave" will allot leaves pro-rata basis by automatically updating Leave Allocation for leaves of this type at intervals set by 'Earned Leave Frequency'.

For example, an Employee is allotted 24 Privilege Leaves in a year, wherein the Privilege Leave is set as Earned Leave with Monthly allotment. In this case, the Employee will earn 2 (24 leaves/12 months) Privilege Leaves at the end of every month. The leave allotment process (background job) will only allot leaves considering the max leaves for the leave type and will round to 'Rounding' for fractions.

![](https://frappehr.com/files/1SrI5mm.png)

> **Note: The initial allocation of this Leave Type will be 0. Leaves will be updated at the end of the Month (or as per the 'Earned Leave Frequency' set).**

### **2.3 Default Leave Types**

There are some pre-loaded Leave Types in the system, as below:

  * **Leave Without Pay:** You can avail these leaves for different purposes, such as extended medical issues, educational purposes, or unavoidable personal reasons. The 'Leave Without Pay' checkbox for this Leave Type is checked by default. The employee does not get paid for such leaves.
  * **Privilege leave:** These are like earned leaves that can be availed for travel, family vacation, and so on.
  * **Sick leave:** You can avail of these leaves if you are unwell.
  * **Compensatory off:** These are compensatory leaves allotted to employees for overtime work. The 'Is Compensatory' checkbox for this Leave Type is checked by default.
  * **Casual leave:** You can avail of this leave to take care of urgent and unseen matters.



## **3\. Related Topics**

  1. [Leave Period](https://docs.frappe.io/hr/leave-period)
  2. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  3. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  4. [Leave Application](https://docs.frappe.io/hr/leave-application)
  5. [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request)
  6. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)



##### Leave Type

rucha edited 8 months ago

×

**Leave Type refers to the types of leaves allocated to an Employee which they can use while making Leave Applications.**

You can create any number of Leave Types based on your company’s requirements.

To access Leave Type, go to:

> **Home > Human Resources > Leaves > Leave Type**

## **1\. How to create a Leave Type**

  1. Go to Leave Type list, click on New.
  2. Enter Leave Type Name.
  3. Enter Maximum Leave Allocation Allowed, Applicable After (Working Days), Maximum Consecutive Leaves Allowed (optional).
  4. Save.



![](https://frappehr.com/files/22ZtSxQ.png)

Below is a detailed explanation of all the fields and checkboxes in Leave Type.

  * **Maximum Leave Allocation Allowed per Leave Period:** This field allows you to set the maximum number of the allocation allowed per Leave Period of this Leave Type while creating the Leave Policy.
  * **Allow Leave Application After (Working Days):** Enter the minimum number of working days here. Only the employees who have worked for this number of days or more since their Date of Joining will be allowed to apply for this particular leave type. Any other leaves (such as Casual Leave, Sick Leave.etc.) availed by the Employees after their joining date will also be considered while calculating working days of the Employee.
  * **Maximum Consecutive Leaves Allowed:** It refers to the maximum number of days this particular Leave Type can be availed at a stretch. If an employee exceeds the maximum number of days, their extended leave will be considered as ‘Leave Without Pay'.
  * **Is Carry Forward:** If checked, the balance leaves of this Leave Type will be carried forward to the next allocation period.
  * **Is Leave Without Pay:** This ensures that the Leave Type will be treated as leaves without pay and salary will get deducted for this Leave Type.
  * **Is Optional Leave:** Optional Leaves are holidays that Employees can choose to avail from a list of holidays published by the company. The Holiday List for Optional Leaves can have any number of holidays, but you can restrict the number of such leaves by setting the Max Days Leave Allowed field.
  * **Allow Negative Balance:** If checked, the system will always allow to apply and approve [Leave Applications](https://docs.frappe.io/hr/leave-application) for the Leave Type, even if there is no leave balance.
  * **Allow Over Allocation:** If checked, the system will allow allocating more leaves than the number of days in the allocation period.
  * **Include holidays within leaves as leaves:** Check this option if you wish to count holidays within leaves as a ‘leave’. For example, if an Employee has applied for leave on Friday and Monday, and Saturday and Sunday are weekly offs, if the 'Include holidays within leaves as leaves' checkbox for the Leave Type is checked, the system will consider Saturday as Sunday as leaves too. Such holidays will be deducted from the total number of leaves.
  * **Is Compensatory:** Compensatory leaves are leaves granted for working overtime or on holidays, normally compensated as an encashable leave. You can check this option to mark the Leave Type as compensatory. An Employee can request for compensatory leaves using [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request).



> **Introduced in version 13**

  * **Is Partially Paid Leaves:** This checkbox ensures that Leave Type will be treated as partially paid and some part of daily earnings will be paid through salary slip. If this checkbox is enabled then a field "Fraction of Daily Salary Per Leave" appears where you can define the fraction of daily salary paid on the partial leave day.



![](https://frappehr.com/files/8IeXPo5.png)

> **Note: The Leave Type can be either Leave Without pay or Partially Paid.**

## **2\. Features**

### **2.1 Leave Encashment**

It is possible that Employees can receive cash from their Employer for unused leaves granted to them in a Leave Period. Not all Leave Types need to be encashable, so, you should set "Allow Encashment" for only those Leave Types which are encashable.

> **Note: Leave encashment is allowed only in the last month of the Leave Period.**

![](https://frappehr.com/files/N7AiiYE.png)

**Non-Encashable Leaves:** This field indicates the number of leave days the Employees won't be able to encash. Above the mentioned days, the Employee is eligible to encash leaves.

For example, if there are 10 leaves of a particular Leave Type which is encashable, and the Employee has 8 leaves left. If Non-Encashable Leaves = 5, the Employee is given encashment of only 8 - 5 = 3 leaves.

**Earning Component:** This field allows you to specify the Salary Component that will be encashed to Employees as a part of their Salary in the Salary Slip.

> **Note: On submitting a** [**Leave Encashment**](https://docs.frappe.io/hr/leave-encashment) **for an Employee, Frappe HR automatically creates an** [**Additional Salary**](https://docs.frappe.io/hr/additional-salary) **which will get added to the Salary Slip of the Employee when processing the next payroll.**

### **2.2 Earned Leave**

Earned Leaves are leaves earned by an Employee after working with the company for a certain amount of time. Checking "Is Earned Leave" will allot leaves pro-rata basis by automatically updating Leave Allocation for leaves of this type at intervals set by 'Earned Leave Frequency'.

For example, an Employee is allotted 24 Privilege Leaves in a year, wherein the Privilege Leave is set as Earned Leave with Monthly allotment. In this case, the Employee will earn 2 (24 leaves/12 months) Privilege Leaves at the end of every month. The leave allotment process (background job) will only allot leaves considering the max leaves for the leave type and will round to 'Rounding' for fractions.

![](https://frappehr.com/files/1SrI5mm.png)

> **Note: The initial allocation of this Leave Type will be 0. Leaves will be updated at the end of the Month (or as per the 'Earned Leave Frequency' set).**

### **2.3 Default Leave Types**

There are some pre-loaded Leave Types in the system, as below:

  * **Leave Without Pay:** You can avail these leaves for different purposes, such as extended medical issues, educational purposes, or unavoidable personal reasons. The 'Leave Without Pay' checkbox for this Leave Type is checked by default. The employee does not get paid for such leaves.
  * **Privilege leave:** These are like earned leaves that can be availed for travel, family vacation, and so on.
  * **Sick leave:** You can avail of these leaves if you are unwell.
  * **Compensatory off:** These are compensatory leaves allotted to employees for overtime work. The 'Is Compensatory' checkbox for this Leave Type is checked by default.
  * **Casual leave:** You can avail of this leave to take care of urgent and unseen matters.



## **3\. Related Topics**

  1. [Leave Period](https://docs.frappe.io/hr/leave-period)
  2. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  3. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  4. [Leave Application](https://docs.frappe.io/hr/leave-application)
  5. [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request)
  6. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)



Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-period

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Period

**A Leave Period is a duration of time for which leaves are allocated.**

Most companies manage leaves based on a Leave Period, corresponding to a calendar year or the fiscal year. To access Leave Period, go to:

> Home > Human Resources > Leaves > Leave Period

## **1\. Prerequisites**

Before creating a Leave Period, it is advisable to create the following:

  * [Company](https://docs.erpnext.com/docs/user/manual/en/company-setup)
  * [Holiday List](https://docs.frappe.io/hr/holiday-list)



## **2\. How to create a Leave Period**

  1. Go to Leave Period list, click on New.
  2. Enter the From Date and To Date of the Leave Period.
  3. Select the Company name for which the Leave Period is applicable.
  4. Save.



The Leave Period also allows you to select a [Holiday List for Optional Leaves](https://docs.frappe.io/hr/holiday-list) (optional) which will be considered for allocating Optional Leaves for the period.

> **Note:** The 'Holiday List for Optional Leaves' is not the same as the usual 'Holiday List'. This list will contain a list of optional holidays only. 'Holiday List for Optional Leaves' can be created from the [Holiday List](https://docs.frappe.io/hr/holiday-list) document. You can create two Holiday Lists for a Leave Period; one containing the usual set of holidays and the other for optional holidays.

Additionally, you can check the 'Is Active' checkbox if you want to enable this particular Leave Period.

![Leave Period](https://frappehr.com/files/leave-period.png)

## **3\. Granting leaves based on Leave Period**

To grant leaves based on Leave Period, use the [Leave Policy Assignment](https://docs.frappe.io/hr/leave-policy-assignment).

## **4\. Related Topics**

  1. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  2. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  3. [Leave Type](https://docs.frappe.io/hr/leave-type)



##### Leave Period

akash edited 8 months ago

×

**A Leave Period is a duration of time for which leaves are allocated.**

Most companies manage leaves based on a Leave Period, corresponding to a calendar year or the fiscal year. To access Leave Period, go to:

> Home > Human Resources > Leaves > Leave Period

## **1\. Prerequisites**

Before creating a Leave Period, it is advisable to create the following:

  * [Company](https://docs.erpnext.com/docs/user/manual/en/company-setup)
  * [Holiday List](https://docs.frappe.io/hr/holiday-list)



## **2\. How to create a Leave Period**

  1. Go to Leave Period list, click on New.
  2. Enter the From Date and To Date of the Leave Period.
  3. Select the Company name for which the Leave Period is applicable.
  4. Save.



The Leave Period also allows you to select a [Holiday List for Optional Leaves](https://docs.frappe.io/hr/holiday-list) (optional) which will be considered for allocating Optional Leaves for the period.

> **Note:** The 'Holiday List for Optional Leaves' is not the same as the usual 'Holiday List'. This list will contain a list of optional holidays only. 'Holiday List for Optional Leaves' can be created from the [Holiday List](https://docs.frappe.io/hr/holiday-list) document. You can create two Holiday Lists for a Leave Period; one containing the usual set of holidays and the other for optional holidays.

Additionally, you can check the 'Is Active' checkbox if you want to enable this particular Leave Period.

![Leave Period](https://frappehr.com/files/leave-period.png)

## **3\. Granting leaves based on Leave Period**

To grant leaves based on Leave Period, use the [Leave Policy Assignment](https://docs.frappe.io/hr/leave-policy-assignment).

## **4\. Related Topics**

  1. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  2. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  3. [Leave Type](https://docs.frappe.io/hr/leave-type)



last

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-policy

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Policy

**The amount of entitled leaves in a Company for an Employee in a Leave Period is known as Leave Policy.**

It is a practice for many enterprises to enforce a general Leave Policy to effectively track and manage Employee leaves. Frappe HR allows you to create and manage multiple Leave Policies and allocate leaves to Employees as defined by the policy.

To access Leave Policy, go to:

> **Home > Human Resources > Leaves > Leave Policy**

## **1\. How to create a Leave Policy**

  1. Go to Leave Policy list, click on New.
  2. Select the Leave Type and enter its Annual Allocation.
  3. Save and Submit.



![Leave Policy](https://frappehr.com/files/leave-policy.png)

Once submitted, you can create [Leave Policy Assignment](https://docs.frappe.io/hr/leave-policy-assignment) to assign this policy to multiple employees.

## **2\. Related Topics**

  1. [Leave Period](https://docs.frappe.io/hr/leave-period)
  2. [Leave Type](https://docs.frappe.io/hr/leave-type)
  3. [Leave Policy Assignment](https://docs.frappe.io/hr/leave-policy-assignment)



##### Leave Policy

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-policy-assignment

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Policy Assignment

> **Introduced in Version 13**

Leave Policy Assignment in Frappe HR is used to assign leaves to employees based on created policies. To access Leave policy assignment, go to:

> **Home > Human Resources > Leaves > Leave Policy Assignment**

## **1\. Prerequisites**

Before creating a Leave Policy Assignment, it is advisable to create the following:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)



## **2\. How to create a Leave Policy Assignment**

  1. Go to Leave Policy Assignment, click on New.
  2. Select Employee and Leave Policy.
  3. Select Assignment based on the following as needed:

     * If "Assignment based on" is set to Leave Period, you need to select the applicable Leave Period. The Effective From and Effective To dates will be set automatically based on the Leave Period selected.
     * If "Assignment based on" is set to Joining Date, the Effective From date will be set to the employee's Date of Joining.
     * If "Assignment based on" is left blank, then you will have to set the Effective From and Effective To date manually.
  4. Save and Submit.



![Leave Policy Assignment](https://frappehr.com/files/leave-policy-assignment.png)

On submission, Leave Allocation documents would be created automatically based on the [Leave Policy](https://docs.frappe.io/hr/leave-policy) as shown below.

![Leave Allocations](https://frappehr.com/files/granted-leaves.png)

## **3\. Features**

### **3.1 Bulk Leave Policy Assignment**

Frappe HR also allows creating Leave Policy Assignments for multiple employees at once.

  1. Go to the Leave Policy Assignment list.
  2. Click on Bulk Leave Policy Assignment.
  3. This will take you to the [Leave Control Panel](https://docs.frappe.io/hr/leave-control-panel), where you can assign Leave Policies in bulk.



![](https://frappehr.com/files/NUL3M8C.png)

### **3.2 Allocating Earned Leaves**

Leave Policy Assignments can also be used to allocate [Earned Leaves](https://docs.frappe.io/hr/configuring-earned-leave).

##### Leave Policy Assignment

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-allocation

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Allocation

**Leave Allocation enables you to allocate a specific number of leaves of a particular type to an Employee.**

To access Leave Allocation, go to:

> Home > Human Resources > Leaves > Leave Allocation

## **1\. Prerequisites**

Before creating a Leave Allocation, it is advisable you create the following documents:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Leave Type](https://docs.frappe.io/hr/leave-type)
  * [Leave Period](https://docs.frappe.io/hr/leave-period)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)



## **2\. How to create a Leave Allocation**

  1. Go to Leave Allocation list, click on New.
  2. Select the Employee, Leave Type, From Date and To Date.
  3. Enter the number of New Leaves Allocated for that particular Leave Type.
  4. Save and Submit.



![Leave Allocation](https://frappehr.com/files/leave-allocation.png)

> **Note:** Enable the 'Add unused leaves from previous allocations' option in case you want to carry forward unused leaves from the previous allocation period for this particular Leave Type.

## **3\. Features**

### 1\. Manually adding leaves to the current allocation

If your earned leave allocation failed due to some reasons like background jobs weren’t running, you can manually allocate leaves for an allocation

Click on **Actions > Allocate Leaves**

![Allocate Leaves Button](/files/allocate.png)

And set the number of leaves you want to allocate. You can also set the **From Date** from when these leaves will be applicable. By default, it will consider today’s date.

![Manual Leave Allocation](/files/manual-allocation.png)

Click on allocate to add leaves to the balance

## **4\. Related Topics**

  1. [Leave Application](https://docs.frappe.io/hr/leave-application)
  2. [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request)
  3. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)
  4. [Leave Block List](https://docs.frappe.io/hr/leave-block-list)
  5. [Holiday List](https://docs.frappe.io/hr/holiday-list)



##### Leave Allocation

rucha edited 8 months ago

×

**Leave Allocation enables you to allocate a specific number of leaves of a particular type to an Employee.**

To access Leave Allocation, go to:

> Home > Human Resources > Leaves > Leave Allocation

## **1\. Prerequisites**

Before creating a Leave Allocation, it is advisable you create the following documents:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Leave Type](https://docs.frappe.io/hr/leave-type)
  * [Leave Period](https://docs.frappe.io/hr/leave-period)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)



## **2\. How to create a Leave Allocation**

  1. Go to Leave Allocation list, click on New.
  2. Select the Employee, Leave Type, From Date and To Date.
  3. Enter the number of New Leaves Allocated for that particular Leave Type.
  4. Save and Submit.



![Leave Allocation](https://frappehr.com/files/leave-allocation.png)

> **Note:** Enable the 'Add unused leaves from previous allocations' option in case you want to carry forward unused leaves from the previous allocation period for this particular Leave Type.

## **3\. Features**

#### 1\. Manually adding leaves to the current allocation

If your earned leave allocation failed due to some reasons like background jobs weren’t running, you can manually allocate leaves for an allocation

Click on **Actions > Allocate Leaves**

![Allocate Leaves Button](/files/allocate.png)

And set the number of leaves you want to allocate. You can also set the **From Date** from when these leaves will be applicable. By default, it will consider today’s date.

![Manual Leave Allocation](/files/Pasted%20image%2020241218133251.png)

Click on allocate to add leaves to the balance

## **4\. Related Topics**

  1. [Leave Application](https://docs.frappe.io/hr/leave-application)
  2. [Compensatory Leave Request](https://docs.frappe.io/hr/compensatory-leave-request)
  3. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)
  4. [Leave Block List](https://docs.frappe.io/hr/leave-block-list)
  5. [Holiday List](https://docs.frappe.io/hr/holiday-list)



Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-control-panel

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Control Panel

The Leave Control Panel allows you to allocate leaves in bulk based on a specific Leave Policy or Leave Type.

> **Home > Human Resources > Leaves > Leave Control Panel**

## **1\. Prerequisites**

Before using the Leave Control Panel, it is advisable to create the following documents:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Leave Period](https://docs.frappe.io/hr/leave-period) (optional)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy) / [Leave Type](https://docs.frappe.io/hr/leave-type)



## **2\. How to allocate leave using the Leave Control Panel**

  1. Choose the appropriate option from Dates Based On and fill in the corresponding data. This would include Leave Period or From Date and/or To Date.
  2. If you wish to assign a Leave Policy, select the intended one. Alternatively, you can allocate leave directly by disabling Allocate Based On Leave Policy, selecting the desired Leave Type, and filling in the corresponding number of New Leaves Allocated.
  3. Select Carry Forward if you wish to add unused leaves from the previous allocation into this one.
  4. Use Quick Filters for filtering employees based on specific fields, or add additional custom filters through Advanced Filters.
  5. Select suitable employees from the Select Employees table below and click on the Allocate Leave button at the top right corner.



![](https://frappehr.com/files/dZbeNka.png)

> **Note: Leave cannot be allocated for past dates.**

[Leave Policy Assignments](https://docs.frappe.io/hr/leave-policy-assignment) and [Leave Allocations](https://docs.frappe.io/hr/leave-allocation) made using the Leave Control Panel are reflected in their respective DocTypes as shown below:

  * Leave Policy Assignment



![](https://frappehr.com/files/91LxRtw.png)

  * Leave Allocation



![](https://frappehr.com/files/2fi9z2Z.png)

##### Leave Control Panel

administrator edited 1 year ago

×

The Leave Control Panel allows you to allocate leaves in bulk based on a specific Leave Policy or Leave Type.

> **Home > Human Resources > Leaves > Leave Control Panel**

## **1\. Prerequisites**

Before using the Leave Control Panel, it is advisable to create the following documents:

  * [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee)

  * [Leave Period](https://frappehr.com/docs/v14/en/leave-period) (optional)

  * [Leave Policy](https://frappehr.com/docs/v14/en/leave-policy) / [Leave Type](https://frappehr.com/docs/v14/en/leave-type)




## **2\. How to allocate leave using the Leave Control Panel**

  1. Choose the appropriate option from Dates Based On and fill in the corresponding data. This would include Leave Period or From Date and/or To Date.

  2. If you wish to assign a Leave Policy, select the intended one. Alternatively, you can allocate leave directly by disabling Allocate Based On Leave Policy, selecting the desired Leave Type, and filling in the corresponding number of New Leaves Allocated.

  3. Select Carry Forward if you wish to add unused leaves from the previous allocation into this one.

  4. Use Quick Filters for filtering employees based on specific fields, or add additional custom filters through Advanced Filters.

  5. Select suitable employees from the Select Employees table below and click on the Allocate Leave button at the top right corner.




![](https://frappehr.com/files/dZbeNka.png)  


> **Note: Leave cannot be allocated for past dates.**

[Leave Policy Assignments](https://frappehr.com/docs/v14/en/leave-policy-assignment) and [Leave Allocations](https://frappehr.com/docs/v14/en/leave-allocation) made using the Leave Control Panel are reflected in their respective DocTypes as shown below:

  * Leave Policy Assignment




![](https://frappehr.com/files/91LxRtw.png)  


  * Leave Allocation




![](https://frappehr.com/files/2fi9z2Z.png)  


last

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-application

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Application

**Leave Application is a formal document created by an Employee to apply for Leaves for a particular time period.**

Frappe HR allows your employees to apply for leaves via Leave Applications and get them approved by the Leave Approvers.

To access Leave Application, go to:

> **Home > Human Resources > Leaves > Leave Application**

## **1\. Prerequisites**

Before you create a Leave Application, it is advisable you have the following documents:

  * [Department](https://docs.frappe.io/hr/department)
  * [Leave Period](https://docs.frappe.io/hr/leave-period)
  * [Holiday List](https://docs.frappe.io/hr/holiday-list)
  * [Leave Type](https://docs.frappe.io/hr/leave-type)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  * [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)



## **2\. How to create a Leave Application**

  1. Go to Leave Application list, click on New.
  2. A table of Allocated Leaves will be shown. Based on the Leaves taken, the available leaves are displayed for each Leave Type.



![Leave Application](https://frappehr.com/files/leave-app.png) 3\. Select the Employee Name and Leave Type. 4\. Set the Leave duration using From Date and To Date. Based on the dates selected, the 'Total Leave Days' and the 'Leave Balance Before Application' fields will be displayed. 5\. If the Leave applied is for a half-day, select the 'Half Day' checkbox. 6\. Enter the Reason for Leave.

![Leave Application](https://frappehr.com/files/leave-app1.png) 7\. Select Leave Approver. 8\. Select the Posting Date of the Leave Application. 9\. Check the 'Follow via Email' checkbox to send notification of the Leave Application to the Leave Approver. 10\. You can also link the Salary Slip of the Employee in the Leave Application for the record.

![Leave Application](https://frappehr.com/files/leave-app3.png) 11\. Click on Save. Once the Employee saves the Leave Application, the status of the Leave Application changes to 'Open', and an email is sent to the Leave Approver for approval. The Leave Approval Notification Template can be configured in [HR Settings](https://docs.frappe.io/hr/hr-settings) under the Leave Settings section. 12\. Once the Leave Approver receives the email, they can Approve, Reject, or Cancel the Leave Application. Once this is done, the Leave Approver can submit the Leave Application. On submission, the status of the document changes accordingly, and an email is sent to the Employee notifying them the same.

> **Note: Leave Application cannot be submitted if the Salary is already processed for the leave period.**

The Leave Application process flow is summarized below:

  * The employee applies for leave through Leave Application.
  * Approver gets notification via email. For this, the "Follow via Email" checkbox should be checked.
  * Approver reviews Leave Application.
  * Approver approves/rejects/cancels Leave Application
  * The employee gets the notification on the status of his/her Leave Application



## **3\. Features**

### **3.1 Setting Leave Approver**

A leave approver is a user who can approve a Leave Application of an Employee. In Frappe HR version 12, Leave Approvers can be set at two levels:

  * **Department Level:** Leave Approvers for each department can be configured in the [Department](https://docs.frappe.io/hr/department) master. Multiple Leave Approvers can be set in a Department. The first Leave Approver in the list will be considered as the default Leave Approver.



![Leave Application - Leave Approvers](https://frappehr.com/files/leave-app4.png)

When an Employee belonging to a particular department applies for leave, the Leave Approvers set in that Employee's department master will be considered as his Leave Approvers. * **Employee Level:** Leave Approvers can also be set Employee-wise in the employee master.

![Leave Application - Leave Approvers](https://frappehr.com/files/employee-level-approvers.png)

If Leave Approvers are set at both Employee-level and Department-level, the Employee-level Leave Approver will be considered as the default Leave Approver in this case.

When a new Leave Application is created, if the selected leave approver does not have access to it, the document is shared with the approver with "submit" permission.

**Tip:** If you want all users to create their own Leave Applications, you can set their “Employee ID” as a match rule in the Leave Application Permission settings. Check [Setting Up Permissions](https://docs.erpnext.com/docs/v144/user/manual/en/setting-up/users-and-permissions/user-permissions) for more information.

> **Additional Notes:**

>   * **Leave Application period must be within a single Leave Allocation period. In case, you are applying for leave across the leave allocation period, you have to create two Leave Application records.**
>   * **Leave Application period must be in the latest Leave Allocation period.**
>   * **Employee cannot apply for leave on the dates which are added in the** [**Leave Block List**](https://docs.frappe.io/hr/leave-block-list)**.**
> 


To understand how Frappe HR allows you configure leaves for employees, check [Leaves](https://docs.frappe.io/hr/leave-management-intro/).

## **3\. Related Topics**

  1. [Leave Type](https://docs.frappe.io/hr/leave-type)
  2. [Leave Period](https://docs.frappe.io/hr/leave-period)
  3. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  4. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  5. [Leave Block List](https://docs.frappe.io/hr/leave-block-list)



##### Leave Application

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/compensatory-leave-request

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Compensatory Leave Request

**Compensatory Leave is a leave that is granted to an Employee as compensation for working overtime or on holidays.**

Frappe HR allows Employees to request for Compensatory Leaves through the Compensatory Leave Request document. It is necessary that the dates mentioned in the Compensatory Leave Request should be in default Holiday List and also that the Employee should have their attendance marked Present.

> **Note:** Only Leave Types which are marked as 'Is Compensatory' can be selected in the Compensatory Leave Request.

To access Compensatory Leave Request, go to:

> Home > Human Resources > Leaves > Compensatory Leave Request

## **1\. Prerequisites**

Before creating a Compensatory Leave Request, it is necessary to create the following documents:

  * [Employee](https://docs.frappe.io/hr/employee)
  * [Leave Period](https://docs.frappe.io/hr/leave-period)
  * [Leave Type](https://docs.frappe.io/hr/leave-type)
  * [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  * [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  * [Holiday List](https://docs.frappe.io/hr/holiday-list)
  * [Attendance](https://docs.frappe.io/hr/attendance)



## **2\. How to create a Compensatory Leave Request**

  1. Go to Compensatory Leave Request list, click on New.
  2. Select the Employee ID. Once selected, The Employee Name and Department will get automatically fetched.
  3. Select Leave Type.
  4. Select Work From Date and Work End Date. This is the date of the day(s) the Employee has worked on, during a Holiday.
  5. Enter the Reason.
  6. Save and Submit.



![Compensatory Leave Request](https://frappehr.com/files/compensatory-leave.png)

On submitting the Compensatory Leave Request, Frappe HR updates the Leave Allocation record for the Compensatory leave type, allowing the Employee to apply for leaves of this type later on depending upon the number of leaves left.

## **3\. Related Topics**

  1. [Leave Application](https://docs.frappe.io/hr/leave-application)
  2. [Leave Encashment](https://docs.frappe.io/hr/leave-encashment)
  3. [Leave Block List](https://docs.frappe.io/hr/leave-block-list)



##### Compensatory Leave Request

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-encashment

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Encashment 

**Leave Encashment refers to an amount of money received in exchange for Leaves not availed by an Employee. You can submit Leave Encashment for Leave Types which are encashable.**

To access Leave Encashment, go to:

> Home > Human Resources > Leaves > Leave Encashment

## **1\. Prerequisites**

Before creating Leave Encashment, it is advisable you create the following documents:

  1. [Employee](https://docs.frappe.io/hr/leave-allocation)
  2. [Leave Type](https://docs.frappe.io/hr/leave-type)
  3. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  4. [Leave Period](https://docs.frappe.io/hr/leave-period)
  5. [Salary Structure](https://docs.frappe.io/hr/salary-structure)
  6. [Salary Structure Assignment](https://docs.frappe.io/hr/salary-structure-assignment)



## **2\. How to create a Leave Encashment**

  1. Go to Leave Encashment list, click on New.
  2. Select Leave Period.
  3. Select the Employee. Once the Employee is selected, the Employee's Department is automatically fetched.
  4. Select Leave Type for which the Leave is encashed. Make sure the Leave Type is encashable (the 'Allow Encashment' checkbox in the Leave Type is checked).
  5. Select Encashment Date, if you want payment via Salary Slip. Based on the date selected, the amount will be encashed in that particular Payroll Entry.
  6. Save and Submit.



![Leave Encashment](https://frappehr.com/files/leave-encashment-new.png)

> **Note:** As you select Employee and Leave Type, Leave Balance and Encashable Days (which is total leave balance minus the threshold days set in Leave Type) will be shown along with the Encashment Amount based on the Leave Encashment per day as configured in the Employee's assigned Salary Structure.

## **3\. Leave Encashment Payment Methods**

In Frappe HR, we allow you to pay the encashment amount via Salary Slip or Payment Entry.

### **3.1 Payment via Payment Entry**

  1. To pay the Leave Encashment amount via Payment Entry you need to check Pay via Payment Entry checkbox. 
  2. After that, it will allow you to select Payable Account and Expense Account. 
  3. After Submitting the record click on the button "Create Payment Entry" which will redirect you to the Payment Entry Form fill in the details, save, and submit.



### **3.2 Payment via Salary Slip**

  1. To pay the Leave Encashment amount via Salary Slip you need to make sure that checkbox Pay via Payment Entry is unchecked.
  2. Select the Encashment Date. Based on the date selected, the amount will be encashed in that particular Payroll Entry. Save and Submit. On submitting a Leave Encashment for an Employee, Frappe HR automatically creates an [Additional Salary](https://docs.frappe.io/hr/additional-salary) which will get added to the Salary Slip of the Employee when processing the payroll.



## **3\. Related Topics**

  1. [Payroll Period](https://docs.frappe.io/hr/payroll-period)
  2. [Payroll Entry](https://docs.frappe.io/hr/payroll-entry)
  3. [Additional Salary](https://docs.frappe.io/hr/additional-salary)



##### Leave Encashment 

aysha edited 6 months ago

×

**Leave Encashment refers to an amount of money received in exchange for Leaves not availed by an Employee. You can submit Leave Encashment for Leave Types which are encashable.**

To access Leave Encashment, go to:

> Home > Human Resources > Leaves > Leave Encashment

## **1\. Prerequisites**

Before creating Leave Encashment, it is advisable you create the following documents:

  1. [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-allocation)

  2. [Leave Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-type)

  3. [Leave Policy](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-policy)

  4. [Leave Period](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-period)

  5. [Salary Structure](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure)

  6. [Salary Structure Assignment](https://frappehr.com/docs/v14/user/manual/en/human-resources/salary-structure-assignment)




## **2\. How to create a Leave Encashment**

  1. Go to Leave Encashment list, click on New.

  2. Select Leave Period.

  3. Select the Employee. Once the Employee is selected, the Employee's Department is automatically fetched.

  4. Select Leave Type for which the Leave is encashed. Make sure the Leave Type is encashable (the 'Allow Encashment' checkbox in the Leave Type is checked).

  5. Select Encashment Date. Based on the date selected, the amount will be encashed in that particular Payroll Entry.

  6. Save and Submit.

![Leave Encashment](https://frappehr.com/files/leave-encashment-new.png)  





> **Note:** As you select Employee and Leave Type, Leave Balance and Encashable Days (which is total leave balance minus the threshold days set in Leave Type) will be shown along with the Encashment Amount based on the Leave Encashment per day as configured in the Employee's assigned Salary Structure.

On submitting a Leave Encashment for an Employee, Frappe HR automatically creates an [Additional Salary](https://frappehr.com/docs/v14/user/manual/en/human-resources/additional-salary) which will get added to the Salary Slip of the Employee when processing the payroll.

## **3\. Related Topics**

  1. [Payroll Period](https://frappehr.com/docs/v14/user/manual/en/human-resources/payroll-period)

  2. [Payroll Entry](https://frappehr.com/docs/v14/user/manual/en/human-resources/payroll-entry)

  3. [Additional Salary](https://frappehr.com/docs/v14/user/manual/en/human-resources/additional-salary)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-block-list

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Block List

**Leave Block List is a list of dates in a year, on which employees cannot apply for leave.**

To access Leave Block List, go to:

> Home > Human Resources > Leaves > Leave Block List

Frappe HR allows you to define a list of Leave Approvers who can approve Leave Applications on blocked days, in case of urgency. You can also define whether the list will be applied to the entire company or on any specific departments.

## **1\. Prerequisites**

Before you create a Leave Block List, it is advisable you have the following documents:

  * [Company](https://docs.erpnext.com/docs/user/manual/en/company-setup)
  * [Department](https://docs.frappe.io/hr/department)
  * [Leave Period](https://docs.frappe.io/hr/leave-period)
  * [Holiday List](https://docs.frappe.io/hr/holiday-list)



## **2\. How to create a Leave Block List**

  1. Go to Leave Block list, and click on New.
  2. Enter Leave Block List Name.
  3. Enter Block Date and Reason in the 'Leave Block List Dates' table.
  4. Enter Users to approve Leave Applications for Blocked Days in the 'Leave BLock List Allowed' table.
  5. Save.



![Leave Block List](https://frappehr.com/files/leave-block-list.png)

> **Note:** Enable the 'Applies to Company' option if you want the Leave Block List to be applicable for the entire Company. If not checked, the list will have to be added to each [Department](https://docs.frappe.io/hr/department) where it has to be applied.

## **3\. Related Topics**

  1. [Leave Type](https://docs.frappe.io/hr/leave-type)
  2. [Leave Period](https://docs.frappe.io/hr/leave-period)
  3. [Leave Policy](https://docs.frappe.io/hr/leave-policy)
  4. [Leave Allocation](https://docs.frappe.io/hr/leave-allocation)
  5. [Leave Application](https://docs.frappe.io/hr/leave-application)



##### Leave Block List

administrator edited 9 months ago

×

**Leave Block List is a list of dates in a year, on which employees cannot apply for leave.**

To access Leave Block List, go to:

> Home > Human Resources > Leaves > Leave Block List

Frappe HR allows you to define a list of Leave Approvers who can approve Leave Applications on blocked days, in case of urgency. You can also define whether the list will be applied to the entire company or on any specific departments.

## **1\. Prerequisites**

Before you create a Leave Block List, it is advisable you have the following documents:

  * [Company](https://frappehr.com/docs/v14/user/manual/en/setting-up/company-setup)

  * [Department](https://frappehr.com/docs/v14/user/manual/en/human-resources/department)

  * [Leave Period](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-period)

  * [Holiday List](https://frappehr.com/docs/v14/user/manual/en/human-resources/holiday-list)




## **2\. How to create a Leave Block List**

  1. Go to Leave Block list, and click on New.

  2. Enter Leave Block List Name.

  3. Enter Block Date and Reason in the 'Leave Block List Dates' table.

  4. Enter Users to approve Leave Applications for Blocked Days in the 'Leave BLock List Allowed' table.

  5. Save.

![Leave Block List](https://frappehr.com/files/leave-block-list.png)  





> **Note:** Enable the 'Applies to Company' option if you want the Leave Block List to be applicable for the entire Company. If not checked, the list will have to be added to each [Department](https://frappehr.com/docs/v14/user/manual/en/human-resources/department) where it has to be applied.

## **3\. Related Topics**

  1. [Leave Type](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-type)

  2. [Leave Period](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-period)

  3. [Leave Policy](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-policy)

  4. [Leave Allocation](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-allocation)

  5. [Leave Application](https://frappehr.com/docs/v14/user/manual/en/human-resources/leave-application)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-ledger-entry

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Ledger Entry

A unified ledger for all leave related transactions for an employee. This maintains all the leaves related transactions, this includes the leave allocations, leave applications and leave encashments of your employees.

![Leave Ledger Entry](https://frappehr.com/files/leave-ledger-entry.png)

##### Leave Ledger Entry

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/leave-ledger-report

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Leave Ledger Report

This report allows you to view the ledger impact for all your leave-related transactions:

  1. Leave Allocation
  2. Leave Application
  3. Leave Encashment



![ledger-report](https://frappehr.com/files/ledger-report.png)

This report gives you an overview of:

  1. Ledger entry creation date
  2. Ledger transaction dates (From Date - To Date)
  3. Leaves (added/consumed/deducted)
  4. Leave Type
  5. Linked transaction details (Type & Name)
  6. Any special flags: Is Carry Forward, Is Expired, Is Leave Without Pay
  7. Company & Holiday List linked to it



You can use the **View Ledger** button in transactions to view the linked ledger entries in the report view

![leave-ledger](https://frappehr.com/files/leave-ledger.gif)

##### Leave Ledger Report

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/appraisal-template

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Appraisal Template

You can define the KRAs and Feedback Criteria based on which employees would be rated in the Appraisal Template.

To access Appraisal Template, go to:

> Home > Human Resources > Performance > Appraisal Template

## 1\. Prerequisites

Before creating an Appraisal Template, you should create the following:

  * KRA: Key Result Areas master to align your goals against
  * Employee Feedback Criteria: Criteria based on which employees should be rated in Performance Feedback and Self Appraisal



## 2\. How to create an Appraisal Template

  1. Go to the Appraisal Template list, and click on New.
  2. Enter a title for the Template. You can keep the title same as the Department or Designation you are creating the template for.
  3. You can optionally set some description.
  4. Add the KRAs (Key Result Areas) along with their weightages.
  5. Add the Criteria based on which employees should be rated in Performance Feedback and Self Appraisal.



![](https://frappehr.com/files/appraisal-template9d1d22.png)

Once you create the templates, you can link them to the respective Designations.

![template link](https://frappehr.com/files/template-link.png)

You can link a template to multiple designations

![](https://frappehr.com/files/template-link-2.png)

## 3\. Related Topics

  1. [Appraisal](/hr/appraisal)
  2. [Employee Performance Feedback](/hr/employee-performance-feedback)



##### Appraisal Template

administrator edited 9 months ago

×

You can define the KRAs and Feedback Criteria based on which employees would be rated in the Appraisal Template.

To access Appraisal Template, go to:

> Home > Human Resources > Performance > Appraisal Template

## 1\. Prerequisites

Before creating an Appraisal Template, you should create the following:

  * KRA: Key Result Areas master to align your goals against

  * Employee Feedback Criteria: Criteria based on which employees should be rated in Performance Feedback and Self Appraisal




## 2\. How to create an Appraisal Template

  1. Go to the Appraisal Template list, and click on New.

  2. Enter a title for the Template. You can keep the title same as the Department or Designation you are creating the template for.

  3. You can optionally set some description.

  4. Add the KRAs (Key Result Areas) along with their weightages.

  5. Add the Criteria based on which employees should be rated in Performance Feedback and Self Appraisal.

![](https://frappehr.com/files/appraisal-template9d1d22.png)  





Once you create the templates, you can link them to the respective Designations.

![template link](https://frappehr.com/files/template-link.png)  


You can link a template to multiple designations

![](https://frappehr.com/files/template-link-2.png)  


## 3\. Related Topics

  1. [Appraisal](/docs/v14/user/manual/en/human-resources/appraisal)

  2. [Employee Performance Feedback](/docs/v14/user/manual/en/human-resources/employee-performance-feedback)




Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/appraisal-cycle

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Appraisal Cycle

An Appraisal Cycle is a defined period of time during which the overall performance of an employee is assessed.

To create an Appraisal Cycle, go to:

> **Home > Human Resources > Performance > Appraisal Cycle**

## **1\. Prerequisites**

Before creating an Appraisal Cycle, you should create [Appraisal Templates](https://docs.frappe.io/hr/appraisal-template) for different designations.

You can link them in the Designation master for fetching them automatically in the cycle or manually select relevant templates in the Appraisees child table.

## **2\. How to create an Appraisal Cycle**

  1. Go to the Appraisal Cycle list, and click on New.
  2. Enter the Cycle Name.
  3. Select the Company and set the Start and End dates.
  4. Select the KRA Evaluation Method:

     * **Automated Based on Goal Progress** (Default): Your KRA/Goal score will be automatically calculated based on your goal completion linked to that KRA. You can read more about [here](https://docs.frappe.io/hr/goal).
     * **Manual Rating** : You can choose to rate Goals/KRAs manually. This is the original evaluation method used until v13.
  5. By default, the Final Score is calculated as the average of Goal Score, Feedback Score, and Self Appraisal Score. To calculate final score using your own formula, enable **_Calculate Final Score based on Formala_** and enter the formula expression.



![](https://frappehr.com/files/0UPkmBl.png)

## **3\. Actions**

### **3.1 Create Appraisals**

You can create appraisals in bulk from the Appraisal Cycle.

  1. In the "Applicable For" tab, set relevant filters to fetch the employees included in this cycle. You can choose to create separate cycles for a particular Branch, Department, or Designation. Ex: Engineering could have an yearly cycle but Sales might need a half-yearly cycle.
  2. After setting the filters, click on the **Get Employees** button and save. This will fetch the employees based on the set filters.
  3. You can check the Employees table to find the fetched employees. You can add more employees to be included in the cycle manually by clicking on the **Add Row** button.
  4. Click on the **Create Appraisals** button. This will create all the Appraisal documents for the selected employees.
  5. Click on **Start** button to start the cycle.



![create appraisalsaa9d04](https://frappehr.com/files/create-appraisalsaa9d04.gif)

If an employee joins after the cycle has already started, you can:

  * Create an Appraisal document for them separately and select the cycle.
  * Or, add rows for new employees in the cycle's Employees child table, save and click on Create Appraisals again. This will create appraisals for the newly added employees.



### **3.2 View Goals**

With this button, you can view the goals linked to the cycle. This button will navigate you to the Goal Tree filtered by the Appraisal Cycle.

![filtered goal tree](https://frappehr.com/files/filtered-goal-tree.png)

## **4\. Features**

### **4.1 Appraisal Stats**

The Stats section in the cycle will give you an overview of:

  * Appraisees: The number of appraisees included in the cycle
  * Self Appraisal Pending
  * Employees without Feedback
  * Employees without Goals



![appraisal stats](https://frappehr.com/files/appraisal-stats.png)

## **5\. Related Topics**

  1. [Appraisal](https://docs.frappe.io/hr/appraisal)
  2. [Goal](https://docs.frappe.io/hr/goal)
  3. [Employee Performance Feedback](https://docs.frappe.io/hr/employee-performance-feedback)



##### Appraisal Cycle

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/appraisal

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Appraisal

**An appraisal is a process in which an employee's performance is documented and evaluated.**

To create an Appraisal Cycle, go to:

> **Home > Human Resources > Performance > Appraisal**

## **1\. Prerequisites**

Before creating an Appraisal, you should create the following:

  * [Appraisal Template](https://docs.frappe.io/hr/appraisal-template)
  * [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle)



## **2\. How to create an Appraisal**

Appraisals can be created in bulk from the [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle).

However, if you want to create an appraisal manually, here are the steps:

  1. Go to the Appraisal list, and click on New.
  2. Select the Employee
  3. Select the Appraisal Cycle.
  4. If the Appraisal Template for the employee is already set in the cycle's Employees child table, it will be auto-fetched. Else you can select a template for the employee. Save.



## **3\. Features**

### **3.1 KRA Evaluation**

Based on the KRA Evaluation Method selected in the [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle) one of the following processes would be applicable:

#### **3.1.1 Automated Based on Goal Progress**

This is the default KRA Evaluation method.

In this method you can create goals and sub-goals aligned to your KRAs.

A KRA vs Goals table will be visible in your appraisal document.

  * **KRA and weightage** : Fetched from the appraisal template
  * **Goal Completion (%)** : Your goal completion percentage will be auto-calculated based on the **progress** of the goals linked to your KRAs.
  * **Goal Score (weighted)** : Based on the weightage assigned to each KRA, the Goal Score will be computed from the completion percentage. For ex: In the screenshot below, the Development KRA has 30% weightage and the employee has completed 75% of the goals. So the goal score is 22.5 out of 30, and so on.



You will finally get a **Total Goal Score** (out of 5) based on the **Goal Score (%)**.

![kra vs goals](https://frappehr.com/files/kra-vs-goals.png)

#### **3.1.2 Manual Rating**

You can choose to rate Goals/KRAs manually. This is the original evaluation method used until v13.

  1. Based on the template selected, the KRAs will be fetched in the Goals section.
  2. Enter the score (0-5) for each KRA.
  3. Based on the weightage mentioned, the **Score Earned** will be calculated for each KRA.
  4. Save.



Based on the Score Earned for each KRA, the system will calculate the Total Score (out of 5) for the Employee.

![manual rating](https://frappehr.com/files/manual-rating.png)

### **3.2 Feedback**

Employee Performance Feedback is captured in the [Employee Performance Feedback](https://docs.frappe.io/hr/employee-performance-feedback) DocType.

But to get an overview of the employee's performance, you can see the history of all the feedback employee has received in the cycle under the Feedback tab.

You can see the rating summary with the average feedback score, no of reviews, and the percentage distribution of the stars the employee has received.

![feedback history](https://frappehr.com/files/feedback-history.png)

> **Note: Only submitted feedback documents are visible in this history and the view is permission sensitive.**

In every review, you can see the reviewer, their designation, avg rating given to the employee, the feedback and the time when the feedback was given. You can also click on the link icon to see the entire Employee Performance Feedback document.

![feedback link](https://frappehr.com/files/feedback-link.png)

If you have the required permissions, you can submit the performance feedback right from this view by clicking on the **New Feedback** button.

![add feedback](https://frappehr.com/files/add-feedback.png)

### **3.3 Self Appraisal**

Under the Self Appraisal tab, employees can rate themselves and add reflections on their performance. The Total Self Score is calculated based on the rating and the weightage against each Feedback Criteria.

![self appraisal](https://frappehr.com/files/self-appraisal.png)

### **3.4 Final Score Calculation**

The Final Score is calculated as an average of your Goal Score, Avg Feedback Score, and Self Appraisal Score.

![final score](https://frappehr.com/files/final-score.png)

### **3.5 View Goals**

You can view the Employee's goals linked to that Appraisal Cycle by clicking on the View Goals button:

![goals appraisal](https://frappehr.com/files/goals-appraisal.png)

### **3.6 Approvals for Appraisals**

Finally, once you have captured all the feedback and updated goals and self appraisal in the Appraisal document, you can submit it.

You can also set up a [Workflow](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/workflows) for approvals before Appraisal submission.

## **4\. Related Topics**

  1. [Goal](https://docs.frappe.io/hr/goal)
  2. [Employee Performance Feedback](https://docs.frappe.io/hr/employee-performance-feedback)



##### Appraisal

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/employee-performance-feedback

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Employee Performance Feedback

**The Employee Performance Feedback document allows you to capture 360° feedback on the employee's performance. Reviewers can rate the employee based on some criteria set up in the Appraisal Template and add a written feedback assessing the employee's performance throughout the cycle.**

To create an Employee Performance Feedback, go to:

> **Home > Human Resources > Performance > Employee Performance Feedback**

## **1\. Prerequisites**

Before creating a Performance Feedback, you should create the following:

  * [Appraisal Template](https://docs.frappe.io/hr/appraisal-template)
  * [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle)
  * [Appraisal Cycle](https://docs.frappe.io/hr/appraisal)



## **2\. How to create an Employee Performance Feedback**

### **2.1 From the Appraisal**

You can directly give feedback to an employee from their Appraisal document. If you have the required permissions, you can submit the performance feedback right from this view by clicking on the **New Feedback** button.

![add feedback](https://frappehr.com/files/add-feedback.png)

For more details about the feedback timeline check [Appraisal](https://docs.frappe.io/hr/appraisal#32-feedback)

### **2.2 Direct creation**

  1. Go to the Employee Performance Feedback list, and click on New.
  2. Select the Employee.
  3. If your session user is linked to some employee, that employee will be auto-selected as the reviewer. Else you can select the reviewer.
  4. Select the Appraisal document against which you want to give the feedback.
  5. The feedback criteria set in the Appraisal Template in the Appraisal document will be pulled into the Feedback Ratings child table.
  6. You can rate the employee for each criteria and add your feedback under the feedback.
  7. Save and Submit.



![perf feedback](https://frappehr.com/files/perf-feedback.png)

## **3\. Features**

### **3.1 Average Feedback Score**

On submitting a feedback, the average feedback score will be updated in the linked appraisal. Cancelling the feedback will update the score again.

### **3.2 Approvals for Appraisals**

If you don't want employees to submit feedback directly, You can also set up a [Workflow](https://docs.erpnext.com/docs/v14/user/manual/en/setting-up/workflows) for approvals before submission.

## **3\. Related Topics**

  1. [Appraisal](https://docs.frappe.io/hr/appraisal)



##### Employee Performance Feedback

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/goal

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Goal

**Goal setting is the process of planning specific, measurable and role-oriented goals that employees work towards in your company.**

To check the goal list, go to:

> **Home > Human Resources > Performance > Goal**

## **1\. Prerequisites**

Before creating a Goal, you should create the following:

  * [Employee](https://docs.frappe.io/hr/employee)



If you want the goal's progress to impact your appraisals, you will also need to create:

  * [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle)
  * [Appraisal](https://docs.frappe.io/hr/appraisal)



## **2\. Goal Setting**

### **2.1 From the tree view**

Since goals have a hierarchical structure, its easier to add new goals from the tree view. You can also update the progress for your child goals from the tree view. Parent goal's progress is auto-calculated based on child goals.

![goal tree](https://frappehr.com/files/goal-tree.png)

You can apply a filter for the Appraisal cycle and your Employee record. These fields will be picked up in the New Goal dialog.

![goal update](https://frappehr.com/files/goal-update.gif)

### **2.2 From the list view**

  1. Go to the Goal list, and click on New.
  2. Enter your goal. You can optionally add a detailed description of your goal.
  3. You can break down your goals into sub-goals for better tracking. To do so, select the goal in the Parent Goal field. Ex: I have a goal called Quality Improvement aligned to the Quality KRA. I can add multiple goals under Quality Improvement like:

     * Bring down GitHub issues by 20%
     * Increase test converage by 30%
  4. Mark the goal as **Is Group** if this goal is going to have sub-goals
  5. Select the Employee.
  6. Set the Start and End Dates for your goal.
  7. If you want the goal's progress to impact your appraisal, select the Appraisal Cycle and tag the KRA for your goal. Now on updating the goal's progress the goal score linked to your KRA will be updated.
  8. Save. The status of your goal is auto-updated based on the progress.



![goal](https://frappehr.com/files/goal.png)

![goal list](https://frappehr.com/files/goal-list.png)

## **3\. Features**

### **3.1 Goal Progress Update**

Whenever a child goal is updated, the parent's goal progress is also updated.

How does a goal's progress affect its parent?

Ex: progress for the goal `child2` is 25%: the average of its children (`child3` and `child4`) progress for the goal `parent` is 12.5%: the average of its children (`child1` and `child2`)
    
    
    parent (12.5%)
    |_ child1 (0%)
    |_ child2 (25%)
            |_ child3 (50%)
            |_ child4 (0%)
    
    
    

Whenever a goal is updated, the average goal completion against the KRA linked to that goal is also updated. Ex: In the screenshot below, the Development KRA has 30% weightage and the employee has completed 75% of the goals. So the goal score is 22.5 out of 30, and so on.

![kra vs goals](https://frappehr.com/files/kra-vs-goals.png)

### **3.2 Archive Goal**

Sometimes you add a goal while planning but later on, you don't want to work on that goal anymore. In that case, you can archive the goal. Archived goal's progress won't contribute to the KRA/Goal score.

![archive](https://frappehr.com/files/archive.png)

### **3.2 Close Goal**

Closing goals will stop employees from making further progress updates but would still contribute to the KRA/Goal score, unlike Archived goals. You can close a goal by clicking the **Status > Goal** button on the Goal form

## **4\. Related Topics**

  1. [Appraisal Cycle](https://docs.frappe.io/hr/appraisal-cycle)
  2. [Appraisal](https://docs.frappe.io/hr/appraisal)



##### Goal

administrator edited 9 months ago

×

**Goal setting is the process of planning specific, measurable and role-oriented goals that employees work towards in your company.**

To check the goal list, go to:

> **Home > Human Resources > Performance > Goal**

## **1\. Prerequisites**

Before creating a Goal, you should create the following:

  * [Employee](https://frappehr.com/docs/v14/user/manual/en/human-resources/employee)




If you want the goal's progress to impact your appraisals, you will also need to create:

  * [Appraisal Cycle](https://frappehr.com/docs/v14/user/manual/en/human-resources/appraisal-cycle)

  * [Appraisal](https://frappehr.com/docs/v14/user/manual/en/human-resources/appraisal)




## **2\. Goal Setting**

### **2.1 From the tree view**

Since goals have a hierarchical structure, its easier to add new goals from the tree view. You can also update the progress for your child goals from the tree view. Parent goal's progress is auto-calculated based on child goals.

![goal tree](https://frappehr.com/files/goal-tree.png)  


You can apply a filter for the Appraisal cycle and your Employee record. These fields will be picked up in the New Goal dialog.

![goal update](https://frappehr.com/files/goal-update.gif)  


### **2.2 From the list view**

  1. Go to the Goal list, and click on New.

  2. Enter your goal. You can optionally add a detailed description of your goal.

  3. You can break down your goals into sub-goals for better tracking. To do so, select the goal in the Parent Goal field. Ex: I have a goal called Quality Improvement aligned to the Quality KRA. I can add multiple goals under Quality Improvement like:

     * Bring down GitHub issues by 20%

     * Increase test converage by 30%

  4. Mark the goal as **Is Group** if this goal is going to have sub-goals

  5. Select the Employee.

  6. Set the Start and End Dates for your goal.

  7. If you want the goal's progress to impact your appraisal, select the Appraisal Cycle and tag the KRA for your goal. Now on updating the goal's progress the goal score linked to your KRA will be updated.

  8. Save. The status of your goal is auto-updated based on the progress.




![goal](https://frappehr.com/files/goal.png)  


![goal list](https://frappehr.com/files/goal-list.png)  


## **3\. Features**

### **3.1 Goal Progress Update**

Whenever a child goal is updated, the parent's goal progress is also updated.

How does a goal's progress affect its parent?

Ex: progress for the goal `child2` is 25%: the average of its children (`child3` and `child4`) progress for the goal `parent` is 12.5%: the average of its children (`child1` and `child2`)
    
    
    parent (12.5%)
    |_ child1 (0%)
    |_ child2 (25%)
            |_ child3 (50%)
            |_ child4 (0%)
      
    

Whenever a goal is updated, the average goal completion against the KRA linked to that goal is also updated. Ex: In the screenshot below, the Development KRA has 30% weightage and the employee has completed 75% of the goals. So the goal score is 22.5 out of 30, and so on.

![kra vs goals](https://frappehr.com/files/kra-vs-goals.png)  


### **3.2 Archive Goal**

Sometimes you add a goal while planning but later on, you don't want to work on that goal anymore. In that case, you can archive the goal. Archived goal's progress won't contribute to the KRA/Goal score.

![archive](https://frappehr.com/files/archive.png)  


### **3.2 Close Goal**

Closing goals will stop employees from making further progress updates but would still contribute to the KRA/Goal score, unlike Archived goals. You can close a goal by clicking the **Status > Goal** button on the Goal form

## **4\. Related Topics**

  1. [Appraisal Cycle](https://frappehr.com/docs/v14/user/manual/en/human-resources/appraisal_cycle)

  2. [Appraisal](https://frappehr.com/docs/v14/user/manual/en/human-resources/appraisal)




  


Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/appraisal-overview-report

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Appraisal Overview Report

This report gives an overview of the Appraisals:

  * **Appraisal Cycle & Appraisal**
  * **Feedback Count** : Number of feedback documents created against the appraisal
  * **Goal Score** : Goal completion score against KRA
  * **Avg Feedback Score** : Avg of all the feedback scores received
  * **Self Score** : Self Appraisal Score
  * **Final Score** : Avg of Goal Score, Feedback Score, and Self Score



![appraisal overview](https://frappehr.com/files/appraisal-overview.png)

##### Appraisal Overview Report

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---


## Source: https://docs.frappe.io/hr/human-resources-reports

This space has **{{ pending_patches_count }}** change(s) pending for review. 

Review changes

# Human Resources Reports

## **1\. Employee Leave Balance**

![Employee Leave Balance](https://frappehr.com/files/employee-leave-balance.png)

## **2\. Salary Register**

Salary Register shows net pay and its components of employee(s) at a glance.

![Monthly Salary Register](https://frappehr.com/files/monthly-salary-register.png)

## **3\. Monthly Attendance Sheet**

Monthly Attendance Sheet shows you the employee-wise attendance status. If you assign shifts to employees, this sheet will also show you shift-wise attendance for each employee.

![Monthly Attendance Sheet](https://frappehr.com/files/monthly-attendance-sheet.png)

If you do not want to see day-wise attendance, and get a summarized view of total absent, present, leave days, etc. you can switch to the summarized view:

![Monthly Attendance Sheet](https://frappehr.com/files/monthly-attendance-summarized.png)

This will show you:

  * Total Present Days
  * Total Absent Days
  * Total Leave Days
  * Total Holidays
  * Total Unmarked Days
  * Number of leaves taken per leave type
  * Total Late Entries
  * Total Early Exits



You can also view the report grouped by Department, Branch, Designation, or Employee Grade.

## **4\. Shift Attendance**

> Introduced in Version 15

Shift Attendance presents and summarizes attendance details for marked shifts:

  * Attendance Details: Employee, Date, Status, In/Out Time, Total Working Hours, Time by which Entry is Late or Exit is Early
  * Shift Details: Shift Name, Shift Start/End Time, Shift Actual Start/End Time



Entries can be further filtered on the basis of:

  * Shift Type
  * Late Entry
  * Early Exit



![](https://frappehr.com/files/KBfhUlt.png)

## **5\. Vehicle Expenses Report**

To track and monitor Vehicle Expenses you can use the Vehicle Expenses report. This report gives a one-stop view of all your Vehicle Expenses month-wise.

![Vehicle Log](https://frappehr.com/files/vehicle-expenses-report.png)

## **6\. Employee Exits**

> Introduced in Version 14

This report gives a summary of Employee Exits:

  * Employee Details: Employee, Reports to, Date of Joining, Relieving Date, Department, Designation
  * Exit Interview
  * Interview Status (Pending/Scheduled/Completed)
  * Final Decision (Employee Retained/Exit Confirmed)
  * Full and Final Statement



This report also provides extra filters for:

  * Interview Pending
  * Questionnaire Pending
  * FnF Pending



Users can set up auto email reports on the above filters to make pending exit tracking easier.

![Monthly Salary Register](https://frappehr.com/files/employee-exits.png)

## **7\. Employee Birthday**

Employee Birthday Report shows month-wise birthdays of your Employees.

![Employee Birthday](https://frappehr.com/files/employee-birthday.png)

## **8\. Employee working on a holiday**

Employee Holiday Attendance shows the list of Employees who attended on Holidays.

![Employee Working on a Holiday](https://frappehr.com/files/employees-working-on-a-holiday.png)

## **9\. Bank Remittance Report**

This report helps you to track bank transactions of payroll entries between companie(s) and employees. It shows the transaction between the bank accounts of the Company and the Employee with the Payment Date.

![Monthly Salary Register](https://frappehr.com/files/bank_remittance_report.png)

## **10\. Loan Repayment Report**

Loan Repayment Report allows you to keep a track of loans by showing the loan amount, interest, payable amount, and EMI. It also shows the paid and outstanding amount.

##### Human Resources Reports

akash edited 1 year ago

×

### No Revisions

Previous Next

##### Page Settings

×

Route

hr/ 

Hide on Sidebar 

Update 

Preview 

Discard 

Save 

Toggle Dropdown

Draft

Title

Content

##### Title

Enter title for the new Wiki Group

Submit

Was this article helpful? Give Feedback

##### Feedback

×

How would you rate this page?

1  2  3  4  5 

How can we make it better?

Submit 

Previous Page Left Next Page Right

---

