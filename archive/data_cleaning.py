#!/usr/bin/env python3
"""
Data Cleaning Script for N-HYPPADEC Employee Data
Transforms nominal_roll.csv into ERPNext-compatible format
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime

def clean_phone_number(phone):
    """Clean and standardize phone numbers to Nigerian format"""
    if pd.isna(phone) or phone == '':
        return ''
    
    # Convert to string and remove any non-digit characters except +
    phone_str = str(phone).strip()
    phone_digits = re.sub(r'[^\d+]', '', phone_str)
    
    # Remove + if present for processing
    digits_only = phone_digits.replace('+', '')
    
    # Handle multiple phone numbers separated by comma
    if ',' in phone_str:
        phones = phone_str.split(',')
        primary_phone = phones[0].strip()
        digits_only = re.sub(r'[^\d]', '', primary_phone)
    
    # Extract 10-digit number (Nigerian mobile numbers)
    if len(digits_only) >= 10:
        if digits_only.startswith('234'):
            # Already has country code
            return '+' + digits_only[:13]  # +234 + 10 digits
        elif digits_only.startswith('0'):
            # Remove leading 0 and add country code
            return '+234' + digits_only[1:11]
        else:
            # Assume it's a 10-digit number without leading 0
            return '+234' + digits_only[:10]
    
    return phone_str  # Return original if can't parse

def clean_name(name):
    """Clean and standardize names"""
    if pd.isna(name) or name == '':
        return ''
    
    # Convert to string and handle special prefixes
    name_str = str(name).strip()
    
    # Remove common prefixes and clean
    prefixes = ['DR.', 'PROF.', 'ENGR.', 'ARC.', 'TPL']
    for prefix in prefixes:
        if name_str.upper().startswith(prefix):
            name_str = name_str[len(prefix):].strip()
    
    # Capitalize properly
    return name_str.title()

def clean_date(date_str):
    """Clean and standardize dates to YYYY-MM-DD format"""
    if pd.isna(date_str) or date_str == '' or str(date_str).strip() == '':
        return None
    
    date_str = str(date_str).strip()
    
    # Try different date formats
    formats = ['%d/%m/%Y', '%d-%m-%Y', '%Y-%m-%d', '%d.%m.%Y']
    
    for fmt in formats:
        try:
            parsed_date = datetime.strptime(date_str, fmt)
            return parsed_date.strftime('%Y-%m-%d')
        except ValueError:
            continue
    
    return None

def clean_grade(grade):
    """Standardize grade format"""
    if pd.isna(grade) or grade == '':
        return ''
    
    grade_str = str(grade).strip().upper()
    
    # Extract HYPPSL number
    if 'HYPPSL' in grade_str:
        return grade_str
    elif grade_str.isdigit():
        return f'HYPPSL {grade_str}'
    
    return grade_str

def clean_department(dept):
    """Standardize department names"""
    if pd.isna(dept) or dept == '':
        return ''
    
    dept_str = str(dept).strip().title()
    
    # Standardize common department variations
    dept_mapping = {
        'Finance & Admin Dept': 'FINANCE & ADMIN DEPT',
        'Finance Division': 'FINANCE DIVISION',
        'Administration Division': 'ADMINISTRATION DIVISION',
        'Engineering Services': 'ENGINEERING SERVICES DIVISION',
        'Technical Services Division': 'TECHNICAL SERVICES DIVISION',
        'Community & Rural Development Division': 'COMMUNITY & RURAL DEVELOPMENT DIVISION',
        'Dams & Operations Divisions': 'DAMS & OPERATIONS DIVISIONS',
        'Press/Public Affairs Unit': 'PRESS/PUBLIC AFFAIRS UNIT',
        'Internal Audit Unit': 'INTERNAL AUDIT UNIT'
    }
    
    for key, value in dept_mapping.items():
        if key.upper() in dept_str.upper():
            return value
    
    return dept_str.upper()

def validate_employee_id(emp_id):
    """Validate and clean employee ID"""
    if pd.isna(emp_id) or emp_id == '':
        return ''
    
    emp_id_str = str(emp_id).strip()
    
    # Ensure proper format: N-HYPPADEC/PF/XXX
    if not emp_id_str.startswith('N-HYPPADEC/PF/'):
        # Try to extract number and format properly
        numbers = re.findall(r'\d+', emp_id_str)
        if numbers:
            return f'N-HYPPADEC/PF/{numbers[0].zfill(3)}'
    
    return emp_id_str

def main():
    """Main data cleaning function"""
    print("Starting data cleaning process...")
    
    try:
        # Read the CSV file
        print("Reading nominal_roll.csv...")
        df = pd.read_csv('nominal_roll.csv')
        print(f"Loaded {len(df)} employee records")
        print(f"Columns found: {list(df.columns)}")
        
        # Create a copy for cleaning
        df_clean = df.copy()
        
        # Clean employee IDs
        print("Cleaning employee IDs...")
        df_clean['FILE NUMBER'] = df_clean['FILE NUMBER'].apply(validate_employee_id)
        
        # Clean names
        print("Cleaning names...")
        df_clean['SURNAME'] = df_clean['SURNAME'].apply(clean_name)
        df_clean['FIRST NAME'] = df_clean['FIRST NAME'].apply(clean_name)
        df_clean['MIDDLE NAME'] = df_clean['MIDDLE NAME'].apply(clean_name)
        
        # Create full name field
        df_clean['FULL_NAME'] = (
            df_clean['SURNAME'].fillna('') + ', ' + 
            df_clean['FIRST NAME'].fillna('') + ' ' + 
            df_clean['MIDDLE NAME'].fillna('')
        ).str.strip().str.replace(r'\s+', ' ', regex=True)
        
        # Clean dates
        print("Cleaning dates...")
        date_columns = ['DOB', 'DOFA', 'DOCA', 'DOPA', 'DOAD']
        for col in date_columns:
            if col in df_clean.columns:
                df_clean[col] = df_clean[col].apply(clean_date)
        
        # Clean phone numbers
        print("Cleaning phone numbers...")
        df_clean['PHONE NO.'] = df_clean['PHONE NO.'].apply(clean_phone_number)
        
        # Clean grades
        print("Cleaning grades...")
        df_clean['GRADE'] = df_clean['GRADE'].apply(clean_grade)
        
        # Clean departments
        print("Cleaning departments...")
        df_clean['DEPARTMENT'] = df_clean['DEPARTMENT'].apply(clean_department)
        
        # Clean gender
        print("Cleaning gender...")
        df_clean['GENDER'] = df_clean['GENDER'].str.upper().map({'M': 'Male', 'F': 'Female'})
        
        # Clean designation
        print("Cleaning designations...")
        df_clean['DESIGNATION'] = df_clean['DESIGNATION'].str.title()
        
        # Clean step values
        print("Cleaning step values...")
        df_clean['STEP'] = pd.to_numeric(df_clean['STEP'], errors='coerce')
        
        # Clean qualifications
        print("Cleaning qualifications...")
        df_clean['QUALIFICATION'] = df_clean['QUALIFICATION'].str.strip()
        
        # Data validation
        print("\nPerforming data validation...")
        
        # Check for duplicate employee IDs
        duplicates = df_clean[df_clean['FILE NUMBER'].duplicated()]
        if not duplicates.empty:
            print(f"Warning: Found {len(duplicates)} duplicate employee IDs")
        
        # Check for missing mandatory fields
        mandatory_fields = ['FILE NUMBER', 'SURNAME', 'FIRST NAME', 'DESIGNATION', 'DEPARTMENT']
        for field in mandatory_fields:
            missing = df_clean[field].isna().sum()
            if missing > 0:
                print(f"Warning: {missing} records missing {field}")
        
        # Validate date sequences
        date_issues = 0
        for idx, row in df_clean.iterrows():
            dofa = row.get('DOFA')
            doca = row.get('DOCA')
            dopa = row.get('DOPA')
            
            if dofa and doca and dofa > doca:
                date_issues += 1
            if doca and dopa and doca > dopa:
                date_issues += 1
        
        if date_issues > 0:
            print(f"Warning: Found {date_issues} records with date sequence issues")
        
        # Save cleaned data
        output_file = 'cleaned_nominal_roll.csv'
        df_clean.to_csv(output_file, index=False)
        print(f"\nCleaned data saved to {output_file}")
        
        # Generate summary report
        print("\n" + "="*50)
        print("DATA CLEANING SUMMARY REPORT")
        print("="*50)
        print(f"Total records processed: {len(df_clean)}")
        print(f"Records with valid phone numbers: {df_clean['PHONE NO.'].str.len().gt(0).sum()}")
        print(f"Records with birth dates: {df_clean['DOB'].notna().sum()}")
        print(f"Records with joining dates: {df_clean['DOFA'].notna().sum()}")
        print(f"Unique departments: {df_clean['DEPARTMENT'].nunique()}")
        print(f"Unique grades: {df_clean['GRADE'].nunique()}")
        
        # Grade distribution
        print("\nGrade Distribution:")
        grade_counts = df_clean['GRADE'].value_counts()
        for grade, count in grade_counts.head(10).items():
            print(f"  {grade}: {count}")
        
        # Department distribution
        print("\nTop 10 Departments:")
        dept_counts = df_clean['DEPARTMENT'].value_counts()
        for dept, count in dept_counts.head(10).items():
            print(f"  {dept}: {count}")
        
        print("\nData cleaning completed successfully!")
        
        # Create ERPNext import template
        print("\nCreating ERPNext import template...")
        create_erpnext_template(df_clean)
        
    except Exception as e:
        print(f"Error during data cleaning: {str(e)}")
        raise

def create_erpnext_template(df_clean):
    """Create ERPNext-compatible import template"""
    
    # Create employee import template
    employee_template = pd.DataFrame({
        'Employee ID': df_clean['FILE NUMBER'],
        'Employee Name': df_clean['FULL_NAME'],
        'First Name': df_clean['FIRST NAME'],
        'Middle Name': df_clean['MIDDLE NAME'],
        'Last Name': df_clean['SURNAME'],
        'Gender': df_clean['GENDER'],
        'Date of Birth': df_clean['DOB'],
        'Date of Joining': df_clean['DOFA'],
        'Designation': df_clean['DESIGNATION'],
        'Department': df_clean['DEPARTMENT'],
        'Grade': df_clean['GRADE'],
        'Step': df_clean['STEP'],
        'Cadre': df_clean['CADRE'],
        'Phone': df_clean['PHONE NO.'],
        'Educational Qualification': df_clean['QUALIFICATION'],
        'State of Origin': df_clean['STATE OF ORIGIN'],
        'LGA': df_clean['LGA'],
        'Status': 'Active'
    })
    
    employee_template.to_csv('erpnext_employee_import.csv', index=False)
    print("ERPNext employee import template saved to: erpnext_employee_import.csv")
    
    # Create department template
    departments = df_clean['DEPARTMENT'].dropna().unique()
    dept_template = pd.DataFrame({
        'Department Name': departments,
        'Parent Department': '',
        'Is Group': 0
    })
    dept_template.to_csv('erpnext_department_import.csv', index=False)
    print("ERPNext department import template saved to: erpnext_department_import.csv")
    
    # Create designation template
    designations = df_clean['DESIGNATION'].dropna().unique()
    desig_template = pd.DataFrame({
        'Designation Name': designations
    })
    desig_template.to_csv('erpnext_designation_import.csv', index=False)
    print("ERPNext designation import template saved to: erpnext_designation_import.csv")

if __name__ == "__main__":
    main()
