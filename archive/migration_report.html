
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>N-HYPPADEC Employee Data Migration Report</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #008753 0%, #006B41 100%); /* Nigerian green */
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
        }
        
        .header h1 {
            color: #008753; /* Nigerian green */
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header .subtitle {
            color: #333;
            font-size: 1.2em;
            margin-bottom: 20px;
        }
        
        .header .meta {
            background: #008753; /* Nigerian green */
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            display: inline-block;
            font-weight: 500;
        }
        
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-icon {
            font-size: 2em;
            margin-right: 15px;
        }
        
        .card-title {
            font-size: 1.1em;
            font-weight: 600;
            color: #008753; /* Nigerian green */
        }
        
        .card-value {
            font-size: 2.5em;
            font-weight: 700;
            color: #008753; /* Nigerian green */
            margin-bottom: 5px;
        }
        
        .card-subtitle {
            color: #666;
            font-size: 0.9em;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            font-size: 1.8em;
            font-weight: 700;
            color: #008753; /* Nigerian green */
            margin-bottom: 20px;
            border-bottom: 3px solid #008753;
            padding-bottom: 10px;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #008753; /* Nigerian green */
            margin-bottom: 15px;
            text-align: center;
        }
        
        .progress-bar {
            background: #ecf0f1;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            background: linear-gradient(90deg, #008753, #006B41); /* Nigerian green gradient */
            transition: width 0.5s ease;
        }
        
        .progress-text {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 0.9em;
            color: #666;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #008753; /* Nigerian green */
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
        }
        
        .badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 500;
        }
        
        .badge-success {
            background: #008753; /* Nigerian green */
            color: white;
        }
        
        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .badge-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .summary-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .summary-number {
            font-size: 2em;
            font-weight: 700;
            color: #008753; /* Nigerian green */
        }
        
        .summary-label {
            color: #666;
            margin-top: 5px;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .card-value {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>N-HYPPADEC Employee Data Migration</h1>
            <div class="subtitle">Payroll Implementation Report</div>
            <div class="meta">
                520 Employee Records Processed
            </div>
        </div>
        
        <!-- Key Metrics -->
        <div class="cards-grid">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">👥</div>
                    <div class="card-title">Total Employees</div>
                </div>
                <div class="card-value">520</div>
                <div class="card-subtitle">Successfully Processed</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">🏢</div>
                    <div class="card-title">Departments</div>
                </div>
                <div class="card-value">43</div>
                <div class="card-subtitle">Organizational Units</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">📋</div>
                    <div class="card-title">Job Positions</div>
                </div>
                <div class="card-value">178</div>
                <div class="card-subtitle">Unique Designations</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">⭐</div>
                    <div class="card-title">Grade Levels</div>
                </div>
                <div class="card-value">14</div>
                <div class="card-subtitle">HYPPSL Classifications</div>
            </div>
        </div>
        
        <!-- Data Quality Section -->
        <div class="section">
            <h2 class="section-title">📈 Data Quality Assessment</h2>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number">99.8%</div>
                    <div class="summary-label">Phone Numbers</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">98.3%</div>
                    <div class="summary-label">Birth Dates</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">12.9%</div>
                    <div class="summary-label">Joining Dates</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">100.0%</div>
                    <div class="summary-label">Employee IDs</div>
                </div>
            </div>
            
            <div style="margin-top: 30px;">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">Data Completeness Progress</h3>
                
                <div>
                    <strong>Phone Numbers:</strong>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 99.8%;"></div>
                    </div>
                    <div class="progress-text">
                        <span>519 out of 520</span>
                        <span>99.8%</span>
                    </div>
                </div>
                
                <div>
                    <strong>Birth Dates:</strong>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 98.3%;"></div>
                    </div>
                    <div class="progress-text">
                        <span>511 out of 520</span>
                        <span>98.3%</span>
                    </div>
                </div>
                
                <div>
                    <strong>Joining Dates:</strong>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 12.9%;"></div>
                    </div>
                    <div class="progress-text">
                        <span>67 out of 520</span>
                        <span>12.9%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Charts Section -->
        <div class="section">
            <h2 class="section-title">📊 Organizational Analysis</h2>
            
            <div class="charts-grid">
                <div class="chart-container">
                    <div class="chart-title">Grade Distribution</div>
                    <canvas id="gradeChart" width="400" height="300"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">Gender Distribution</div>
                    <canvas id="genderChart" width="400" height="300"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">Top Departments</div>
                    <canvas id="departmentChart" width="400" height="300"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">State Distribution (Top 10)</div>
                    <canvas id="stateChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Top Departments Table -->
        <div class="section">
            <h2 class="section-title">🏢 Department Breakdown</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Department</th>
                        <th>Employee Count</th>
                        <th>Percentage</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    
        <tr>
            <td>GENERAL SERVICES DIVISION</td>
            <td>67</td>
            <td>12.9%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>ADMINISTRATION DIVISION</td>
            <td>49</td>
            <td>9.4%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>DAMS & OPERATIONS DIVISIONS</td>
            <td>47</td>
            <td>9.0%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>ENGINEERING SERVICES DIVISION</td>
            <td>33</td>
            <td>6.3%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>ICT UNIT</td>
            <td>30</td>
            <td>5.8%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>FINANCE DIVISION</td>
            <td>27</td>
            <td>5.2%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>ADVOCACY & MOBILIZATION DIVISION</td>
            <td>21</td>
            <td>4.0%</td>
            <td><span class="badge badge-success">Large</span></td>
        </tr>
        
        <tr>
            <td>POLICY & PLANNING DIVISION</td>
            <td>20</td>
            <td>3.8%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>TECHNICAL SERVICES DIVISION</td>
            <td>19</td>
            <td>3.7%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>ENVIRONMENTAL HAZARD MITIGATION DIVISION</td>
            <td>18</td>
            <td>3.5%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>RESEARCH & STATISTIC DIVISION</td>
            <td>18</td>
            <td>3.5%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>RELIEF MANAGRMENT DIVISION</td>
            <td>14</td>
            <td>2.7%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>STOCK VERIFICATION UNIT</td>
            <td>14</td>
            <td>2.7%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>GENDER DIVISION</td>
            <td>14</td>
            <td>2.7%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
        <tr>
            <td>COMMUNITY & RURAL DEVELOPMENT DIVISION</td>
            <td>14</td>
            <td>2.7%</td>
            <td><span class="badge badge-warning">Medium</span></td>
        </tr>
        
                </tbody>
            </table>
        </div>
        
        <!-- Grade Analysis -->
        <div class="section">
            <h2 class="section-title">⭐ Grade Level Analysis</h2>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Grade Level</th>
                        <th>Employee Count</th>
                        <th>Percentage</th>
                        <th>Category</th>
                    </tr>
                </thead>
                <tbody>
                    
        <tr>
            <td>HYPPSL 08</td>
            <td>198</td>
            <td>38.1%</td>
            <td>Junior Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 09</td>
            <td>127</td>
            <td>24.4%</td>
            <td>Junior Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 04</td>
            <td>54</td>
            <td>10.4%</td>
            <td>Technical Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 10</td>
            <td>41</td>
            <td>7.9%</td>
            <td>Middle Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 07</td>
            <td>27</td>
            <td>5.2%</td>
            <td>Support Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 12</td>
            <td>21</td>
            <td>4.0%</td>
            <td>Senior Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 06</td>
            <td>13</td>
            <td>2.5%</td>
            <td>Support Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 05</td>
            <td>10</td>
            <td>1.9%</td>
            <td>Support Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 13</td>
            <td>9</td>
            <td>1.7%</td>
            <td>Senior Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 15</td>
            <td>7</td>
            <td>1.3%</td>
            <td>Management</td>
        </tr>
        
        <tr>
            <td>HYPPSL 17</td>
            <td>5</td>
            <td>1.0%</td>
            <td>Executive</td>
        </tr>
        
        <tr>
            <td>HYPPSL 14</td>
            <td>3</td>
            <td>0.6%</td>
            <td>Senior Staff</td>
        </tr>
        
        <tr>
            <td>HYPPSL 16</td>
            <td>2</td>
            <td>0.4%</td>
            <td>Senior Management</td>
        </tr>
        
        <tr>
            <td>HYPPSL 03</td>
            <td>2</td>
            <td>0.4%</td>
            <td>Support Staff</td>
        </tr>
        
                </tbody>
            </table>
        </div>
        
        <!-- Issues and Recommendations -->
        <div class="section">
            <h2 class="section-title">⚠️ Issues Identified & Recommendations</h2>
            
            <div style="margin-bottom: 20px;">
                <h3 style="color: #e74c3c; margin-bottom: 10px;">🚨 Critical Issues</h3>
                <ul style='padding-left: 20px; color: #e74c3c;'><li style='margin-bottom: 8px;'>Found 1 duplicate employee ID(s)</li><li style='margin-bottom: 8px;'>1 records missing grade information</li></ul>
            </div>
            
            <div>
                <h3 style="color: #f39c12; margin-bottom: 10px;">💡 Recommendations</h3>
                <ul style="padding-left: 20px; color: #2c3e50;">
                    <li style="margin-bottom: 8px;">Review and resolve duplicate employee ID: Manual verification required</li>
                    <li style="margin-bottom: 8px;">Improve joining date data collection: Only 12.9% have DOFA dates</li>
                    <li style="margin-bottom: 8px;">Standardize department structure for Payroll Solution hierarchy</li>
                    <li style="margin-bottom: 8px;">Implement data validation rules in Payroll Solution to prevent future issues</li>
                    <li style="margin-bottom: 8px;">Create employee onboarding checklist for new hires</li>
                </ul>
            </div>
        </div>
        
        <!-- Migration Status -->
        <div class="section">
            <h2 class="section-title">✅ Migration Readiness</h2>
            
            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-number">4</div>
                    <div class="summary-label">Import Files Generated</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">99.8%</div>
                    <div class="summary-label">Data Quality Score</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">100%</div>
                    <div class="summary-label">Records Processed</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number">Ready</div>
                    <div class="summary-label">Migration Status</div>
                </div>
            </div>
            
            <div style="margin-top: 20px;">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">Generated Files for Payroll Solution Import:</h3>
                <ul style="padding-left: 20px; color: #2c3e50;">
                    <li style="margin-bottom: 8px;"> <strong>payroll_employee_import.csv</strong> - Main employee records</li>
                    <li style="margin-bottom: 8px;"> <strong>payroll_department_import.csv</strong> - Department structure</li>
                    <li style="margin-bottom: 8px;"> <strong>payroll_designation_import.csv</strong> - Job designations</li>
                    <li style="margin-bottom: 8px;"> <strong>cleaned_nominal_roll.csv</strong> - Cleaned source data</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>Generated by Geomatix | Payroll Solution Implementation</p>
        <p>© 2025 Geomatix</p>
    </div>
    
    <script>
        // Chart.js configurations and data
        const chartColors = [
            '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
            '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
        ];
        
        // Grade Distribution Chart
        const gradeData = [["HYPPSL 08", 198], ["HYPPSL 09", 127], ["HYPPSL 04", 54], ["HYPPSL 10", 41], ["HYPPSL 07", 27], ["HYPPSL 12", 21], ["HYPPSL 06", 13], ["HYPPSL 05", 10], ["HYPPSL 13", 9], ["HYPPSL 15", 7]];
        new Chart(document.getElementById('gradeChart'), {
            type: 'doughnut',
            data: {
                labels: gradeData.map(item => item[0]),
                datasets: [{
                    data: gradeData.map(item => item[1]),
                    backgroundColor: chartColors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });
        
        // Gender Distribution Chart
        const genderData = [["Male", 409], ["Female", 110]];
        new Chart(document.getElementById('genderChart'), {
            type: 'pie',
            data: {
                labels: genderData.map(item => item[0]),
                datasets: [{
                    data: genderData.map(item => item[1]),
                    backgroundColor: ['#3498db', '#e74c3c'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                }
            }
        });
        
        // Department Distribution Chart
        const deptData = [["GENERAL SERVICES DIVISION", 67], ["ADMINISTRATION DIVISION", 49], ["DAMS & OPERATIONS DIVISIONS", 47], ["ENGINEERING SERVICES DIVISION", 33], ["ICT UNIT", 30], ["FINANCE DIVISION", 27], ["ADVOCACY & MOBILIZATION DIVISION", 21], ["POLICY & PLANNING DIVISION", 20]];
        new Chart(document.getElementById('departmentChart'), {
            type: 'bar',
            data: {
                labels: deptData.map(item => item[0].length > 20 ? item[0].substring(0, 20) + '...' : item[0]),
                datasets: [{
                    label: 'Employees',
                    data: deptData.map(item => item[1]),
                    backgroundColor: '#3498db',
                    borderColor: '#2980b9',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // State Distribution Chart
        const stateData = [["NIGER", 150], ["KEBBI", 92], ["KOGI", 47], ["KWARA", 43], ["BENUE", 42], ["PLATEAU", 20], ["SOKOTO", 17], ["KANO", 12], ["JIGAWA", 9], ["TARABA", 9]];
        new Chart(document.getElementById('stateChart'), {
            type: 'horizontalBar',
            data: {
                labels: stateData.map(item => item[0]),
                datasets: [{
                    label: 'Employees',
                    data: stateData.map(item => item[1]),
                    backgroundColor: '#2ecc71',
                    borderColor: '#27ae60',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    x: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
</body>
</html>
