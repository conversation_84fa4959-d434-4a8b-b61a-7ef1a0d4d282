#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
download_docs_to_md.py
~~~~~~~~~~~~~~~~~~~~~~
Download a collection of documentation pages and concatenate them into three
Markdown files: hr.md, payroll.md and project.md.

Requirements
------------
pip install requests beautifulsoup4 html2text tqdm

Author:  <your‑name>
Date:    2025‑08‑11
"""

import sys
import time
from pathlib import Path
from typing import List, Tuple, Dict

import requests
from bs4 import BeautifulSoup
import html2text
from tqdm import tqdm

# ----------------------------------------------------------------------
# 1️⃣  Configuration – URLs → destination markdown file
# ----------------------------------------------------------------------
URL_GROUPS: Dict[str, List[str]] = {
    "hr.md": [
        # General (non‑Frappe) page – you can keep it in the HR file
        "https://www.nhyppadec.gov.ng/background",

        # All HR‑related docs (except the ones that are strictly Payroll)
        "https://docs.frappe.io/hr/employee",
        "https://docs.frappe.io/hr/employment-type",
        "https://docs.frappe.io/hr/branch",
        "https://docs.frappe.io/hr/department",
        "https://docs.frappe.io/hr/designation",
        "https://docs.frappe.io/hr/employee-grade",
        "https://docs.frappe.io/hr/employee-group",
        "https://docs.frappe.io/hr/employee-health-insurance",
        "https://docs.frappe.io/hr/organizational-chart",
        "https://docs.frappe.io/hr/attendance",
        "https://docs.frappe.io/hr/employee-attendance-tool",
        "https://docs.frappe.io/hr/attendance-request",
        "https://docs.frappe.io/hr/upload-attendance",
        "https://docs.frappe.io/hr/employee-checkin",
        "https://docs.frappe.io/hr/auto-attendance",
        "https://docs.frappe.io/hr/leaves",
        "https://docs.frappe.io/hr/holiday-list",
        "https://docs.frappe.io/hr/leave-type",
        "https://docs.frappe.io/hr/leave-period",
        "https://docs.frappe.io/hr/leave-policy",
        "https://docs.frappe.io/hr/leave-policy-assignment",
        "https://docs.frappe.io/hr/leave-allocation",
        "https://docs.frappe.io/hr/leave-control-panel",
        "https://docs.frappe.io/hr/leave-application",
        "https://docs.frappe.io/hr/compensatory-leave-request",
        "https://docs.frappe.io/hr/leave-encashment",
        "https://docs.frappe.io/hr/leave-block-list",
        "https://docs.frappe.io/hr/leave-ledger-entry",
        "https://docs.frappe.io/hr/leave-ledger-report",
        "https://docs.frappe.io/hr/appraisal-template",
        "https://docs.frappe.io/hr/appraisal-cycle",
        "https://docs.frappe.io/hr/appraisal",
        "https://docs.frappe.io/hr/employee-performance-feedback",
        "https://docs.frappe.io/hr/goal",
        "https://docs.frappe.io/hr/appraisal-overview-report",
        "https://docs.frappe.io/hr/human-resources-reports",
    ],
    "payroll.md": [
        "https://docs.frappe.io/hr/payroll-setup",
        "https://docs.frappe.io/hr/payroll-management",
        "https://docs.frappe.io/hr/payroll-period",
        "https://docs.frappe.io/hr/salary-component",
        "https://docs.frappe.io/hr/salary-structure",
        "https://docs.frappe.io/hr/salary-structure-assignment",
        "https://docs.frappe.io/hr/salary-structure-assignment-tool",
        "https://docs.frappe.io/hr/salary-slip",
        "https://docs.frappe.io/hr/payroll-entry",
        "https://docs.frappe.io/hr/retention-bonus",
    ],
    "project.md": [
        "https://docs.frappe.io/erpnext/user/manual/en/project-reports",
        "https://docs.frappe.io/erpnext/user/manual/en/project-essentials",
        "https://docs.frappe.io/erpnext/user/manual/en/time-tracking",
    ],
}

# ----------------------------------------------------------------------
# 2️⃣  Helper: fetch a page with retries
# ----------------------------------------------------------------------
def fetch_html(url: str, timeout: int = 10, max_retries: int = 3) -> str:
    """Return raw HTML for *url* or raise after *max_retries* attempts."""
    headers = {
        # Mimic a regular browser – some sites block the default python‑requests UA
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/********* Safari/537.36"
        )
    }

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            return response.text
        except requests.RequestException as exc:
            print(f"[{url}] attempt {attempt}/{max_retries} failed: {exc}", file=sys.stderr)
            if attempt == max_retries:
                raise
            time.sleep(2 ** attempt)  # exponential back‑off


# ----------------------------------------------------------------------
# 3️⃣  Helper: pull the *main* article from the HTML
# ----------------------------------------------------------------------
def extract_main_content(html: str) -> str:
    """
    Returns the portion of *html* that most likely contains the readable article.
    For Frappe docs the article is wrapped in <article>.  For other sites we
    fall back to the whole <body>.
    """
    soup = BeautifulSoup(html, "html.parser")

    # 1️⃣  Try the typical Frappe/ERPNext wrapper
    article = soup.find("article")
    if article:
        return str(article)

    # 2️⃣  Fall back to <main>, then to <body>
    for selector in ("main", "body"):
        tag = soup.find(selector)
        if tag:
            return str(tag)

    # 3️⃣  As a last resort return the whole document
    return html


# ----------------------------------------------------------------------
# 4️⃣  Convert HTML → Markdown
# ----------------------------------------------------------------------
def html_to_markdown(html: str) -> str:
    """Thin wrapper around html2text – cleans up a few quirks."""
    h = html2text.HTML2Text()
    h.body_width = 0          # no line‑wrapping
    h.ignore_links = False
    h.ignore_images = False
    h.unicode_snob = True
    return h.handle(html)


# ----------------------------------------------------------------------
# 5️⃣  Main routine – iterate groups, download, convert, write
# ----------------------------------------------------------------------
def main(output_dir: Path = Path(".")) -> None:
    output_dir.mkdir(parents=True, exist_ok=True)

    for md_filename, urls in URL_GROUPS.items():
        target_file = output_dir / md_filename
        print(f"\n=== Writing {target_file} ({len(urls)} pages) ===")

        # Open file once and append each converted page
        with target_file.open("w", encoding="utf-8") as f_md:
            for url in tqdm(urls, desc=f"Processing {md_filename}", unit="url"):
                try:
                    html = fetch_html(url)
                    main_html = extract_main_content(html)
                    md_body = html_to_markdown(main_html)

                    # --- Write a clear separator for each page ---
                    f_md.write(f"\n## Source: {url}\n\n")
                    f_md.write(md_body.strip())
                    f_md.write("\n\n---\n\n")   # horizontal rule between pages

                except Exception as exc:
                    # Do not abort the whole run – just note the failure
                    err_msg = f"*ERROR* while processing [{url}] – {exc}"
                    print(err_msg, file=sys.stderr)
                    f_md.write(f"\n## Source: {url}\n\n")
                    f_md.write(f"*{err_msg}*\n\n---\n\n")

        print(f"✅ Finished {target_file}")


# ----------------------------------------------------------------------
# 6️⃣  Entry point
# ----------------------------------------------------------------------
if __name__ == "__main__":
    # Optional: you can supply a custom folder:  python download_docs_to_md.py ./docs
    out_dir = Path(sys.argv[1]) if len(sys.argv) > 1 else Path(".")
    main(out_dir)
