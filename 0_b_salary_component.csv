"Name","Abbr","Type","Description","Depends on Payment Days","Is Tax Applicable","Deduct Full Tax on Selected Payroll Date","Variable Based On Taxable Salary","Is Income Tax Component","Exempted from Income Tax","Round to the Nearest Integer","Statistical Component","Do Not Include in Total","Remove if Zero Valued","Disabled","Condition","Amount","Amount based on formula","Formula","Is Flexible Benefit","Max Benefit Amount (Yearly)","Pay Against Benefit Claim","Only Tax Impact (Cannot Claim But Part of Taxable Income)","Create Separate Payment Entry Against Benefit Claim","ID (Accounts)","Account (Accounts)","Company (Accounts)"
"Gross Income for CRA","GI_FOR_CRA","Deduction","",1,0,0,0,0,0,0,1,0,1,0,"",0.0,1,"TAE - ( (NPF * 12) + (NHF * 12) + (NHIS * 12) )",0,0.0,0,0,0,"","",""
"Total Annual Earnings","TAE","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"",0.0,1,"(
  (base * 0.108) + # Basic Salary
  (base * 0.108) + # Wardrobe Allowance
  (base * 0.108) + # Hazard Allowance
  ( (base * 0.10) if employee.grade not in [""HYPPSL 17"", ""HYPPSL 16"", ""HYPPSL 15""] else 0 ) + # Rural Posting Allowance
  (base * 0.07) + # Education Allowance
  (base * 0.05) + # Domestic Allowance
  (base * 0.03) + # Utility Allowance
  (base * 0.03) + # Medical Allowance
  (base * 0.02) + # Entertainment Allowance
  (base * 0.011) + # Meal Allowance
  (base * 0.007) + # Telephone Allowance
  (base * 0.005) + # Internet Allowance
  (base * 0.004) # DSTV Allowance",0,0.0,0,0,0,"","",""
"Annual Chargeable Income","ACI","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"",0.0,1,"TAE - ACRA",0,0.0,0,0,0,"","",""
"Annual CRA","ACRA","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"",0.0,1,"(GI_FOR_CRA * 0.2) + 200000",0,0.0,0,0,0,"","",""
"Adjusted Annual Gross Income","AAGI","Earning","",0,0,0,0,1,0,0,1,0,1,0,"",45.0,1,"AGI - ANPF - ANHF - ANHIS",0,0.0,0,0,0,"","",""
"Annual NHIS","ANHIS","Deduction","Annual NHIS ",1,0,0,0,1,0,0,1,0,1,0,"",0.0,1,"NHIS * 12 ",0,0.0,0,0,0,"","",""
"Annual NHF","ANHF","Deduction","",1,0,0,0,1,0,0,1,0,1,0,"",0.0,1,"NHF * 12 ",0,0.0,0,0,0,"","",""
"Annual NPF","ANPF","Deduction","",1,0,0,0,1,0,0,1,0,1,0,"",0.0,1,"NPF * 12 ",0,0.0,0,0,0,"","",""
"Total Relief","TR","Deduction","",0,0,0,0,0,0,0,1,0,0,0,"",0.0,1,"CRA+NPF+NHF+NHIS",0,0.0,0,0,0,"","",""
"Tax Payable 6","T6","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI > 3200000",0.0,1,"(ACI - 3200000) * 0.24 + 560000",0,0.0,0,0,0,"","",""
"Tax Payable 5","T5","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI > 1600000 and ACI <= 3200000",0.0,1,"(ACI - 1600000) * 0.21 + 224000",0,0.0,0,0,0,"","",""
"Tax Payable 4","T4","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI > 1100000 and ACI <= 1600000",0.0,1,"(ACI - 1100000) * 0.19 + 129000",0,0.0,0,0,0,"","",""
"Tax Payable 3","T3","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI > 600000 and ACI <= 1100000",0.0,1,"(ACI - 600000) * 0.15 + 54000",0,0.0,0,0,0,"","",""
"Tax Payable 2","T2","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI > 300000 and ACI <= 600000",0.0,1,"(ACI - 300000) * 0.11 + 21000",0,0.0,0,0,0,"","",""
"Tax Payable 1","T1","Deduction","",0,0,0,0,0,0,0,1,0,1,0,"ACI <= 300000",0.0,1,"ACI*0.07",0,0.0,0,0,0,"","",""
"Gross Income","GI","Deduction","",0,0,0,0,1,0,0,1,0,0,0," ",0.0,1,"(base * 0.108) + (base * 0.108) + (base * 0.108) + ((base * 0.10) if grade not in [""HYPPSL 17"", ""HYPPSL 16"", ""HYPPSL 15""] else 0) + (base * 0.07) + (base * 0.05) + (base * 0.03) + (base * 0.03) + (base * 0.02) + (base * 0.011) + (base * 0.007) + (base * 0.005) + (base * 0.004)
",0,0.0,0,0,0,"n017dnl09v","2120 - Payroll Payable - NHYP","NHYPPADEC"
"PAYE","PAYE","Deduction","",0,0,0,0,1,0,1,0,0,1,0,"",6.0,1,"( ( (ACI > 0 and ACI <= 300000) * (ACI * 0.07) ) + ( (ACI > 300000 and ACI <= 600000) * ((ACI - 300000) * 0.11 + 21000) ) + ( (ACI > 600000 and ACI <= 1100000) * ((ACI - 600000) * 0.15 + 54000) ) + ( (ACI > 1100000 and ACI <= 1600000) * ((ACI - 1100000) * 0.19 + 129000) ) + ( (ACI > 1600000 and ACI <= 3200000) * ((ACI - 1600000) * 0.21 + 224000) ) + ( (ACI > 3200000) * ((ACI - 3200000) * 0.24 + 560000) ) ) / 12",0,0.0,0,0,0,"klbfes0gbm","2160 - PAYE Payable - NHYP","NHYPPADEC"
"NHF","NHF","Deduction","",0,0,0,0,0,0,0,0,0,1,0,"",0.0,1,".025 * BS",0,0.0,0,0,0,"530qm4kstt","2140 - NHF Payable - NHYP","NHYPPADEC"
"NHIS","NHIS","Deduction","",0,0,0,0,0,0,0,0,0,1,0,"",0.0,1,".05 * BS",0,0.0,0,0,0,"2er3euep8l","2150 - NHIS Payable - NHYP","NHYPPADEC"
"NPF","NPF","Deduction","",0,0,0,0,0,0,0,0,0,1,0,"",2000.0,1,".08 * BS ",0,0.0,0,0,0,"1rgs8e3627","2130 - NPF Payable - NHYP","NHYPPADEC"
"Wardrobe Allowance","WA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.108/12",0,0.0,0,0,0,"mmktb4dl8j","5213 - Salary - NHYP","NHYPPADEC"
"Hazard Allowance","HAZ","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.108/12",0,0.0,0,0,0,"mgdrt8g0ri","5213 - Salary - NHYP","NHYPPADEC"
"Rural Posting Allowance","RPA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"grade not in [""HYPPSL 17"", ""HYPPSL 16"", ""HYPPSL 15""] ",0.0,1,"base * 0.10/12",0,0.0,0,0,0,"l91cm5pvak","5213 - Salary - NHYP","NHYPPADEC"
"Education Allowance","EDU","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.07/12",0,0.0,0,0,0,"bl80so6trj","5213 - Salary - NHYP","NHYPPADEC"
"Telephone Allowance","TEL","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.007/12",0,0.0,0,0,0,"a5qcuqdmca","5213 - Salary - NHYP","NHYPPADEC"
"Internet Allowance","INT","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.005/12",0,0.0,0,0,0,"9ett664cim","5213 - Salary - NHYP","NHYPPADEC"
"DSTV Allowance","DSTV","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.004/12",0,0.0,0,0,0,"96ota62lud","5213 - Salary - NHYP","NHYPPADEC"
"Medical Allowance","MDA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.03/12",0,0.0,0,0,0,"8rmjoh2eg1","5213 - Salary - NHYP","NHYPPADEC"
"Utility Allowance","UA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.03/12",0,0.0,0,0,0,"6g5cr91im1","5213 - Salary - NHYP","NHYPPADEC"
"Domestic Allowance","DA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.05/12",0,0.0,0,0,0,"68f4asu1tb","5213 - Salary - NHYP","NHYPPADEC"
"Entertainment Allowance","EA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.02/12",0,0.0,0,0,0,"5e2so02v0m","5213 - Salary - NHYP","NHYPPADEC"
"Meal Allowance","MA","Earning","",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base * 0.011/12",0,0.0,0,0,0,"sp9h6kkoni","5213 - Salary - NHYP","NHYPPADEC"
"Basic","BS","Earning","Employee Base Basic",0,1,0,0,0,0,0,0,0,1,0,"",0.0,1,"base*0.108/12",0,0.0,0,0,0,"js49g5giah","5213 - Salary - NHYP","NHYPPADEC"
